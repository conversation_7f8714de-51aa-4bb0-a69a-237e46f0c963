<?php

declare(strict_types=1);

namespace App\Service;

use App\DTO\UserEvent;
use App\Gateway\ProfessionalworksGatewayFactory;
use App\Security\UserInterface;
use App\Util\EmailHistoryLogger;
use Carbon\Carbon;
use Exception;

class ProfessionalWorksEventService
{
    public const BASE_URL = '/events/event/events';
    public const EVENT_TYPE_USER_DELETED = 'user_deleted';
    public const EVENT_TYPE_USER_SOFT_DELETED = 'user_soft_deleted';
    public const EVENT_TYPE_EMAIL_SETTINGS_ERROR = 'email_settings_error';

    public function __construct(
        private readonly ProfessionalworksGatewayFactory $gatewayFactory,
        private readonly EmailHistoryLogger $logger,
        private readonly LoggerInterface $systemLogger,
    ) {
    }

    /**
     * @param array<string>|null $eventTypes
     *
     * @return array<UserEvent>
     *
     * @throws Exception
     */
    public function getUserEvents(Carbon $fromDate, array $eventTypes = null): array
    {
        $params = [];
        $params['fromDate'] = urlencode($fromDate->toDateTimeString());

        if ($eventTypes !== null) {
            $params['types'] = $eventTypes;
        }

        $gateway = $this->gatewayFactory->createForSystem();
        $response = $gateway->get(self::BASE_URL, $params);

        if ($response->getStatusCode() !== 200) {
            throw new Exception("Error while fetching user account events: " . $response->getStatusCode());
        }

        $data = json_decode($response->getBody()->getContents(), true);

        if (!is_array($data)) {
            throw new Exception("Error decoding response: ");
        }

        if (json_last_error() !== 0) {
            throw new Exception("Error decoding response: " . json_last_error_msg());
        }

        if (($data['status'] ?? '') !== 'success') {
            throw new Exception("Error in status, did not receive a success: " . $data['status']);
        }

        if (!isset($data['data'])) {
            throw new Exception("Error in response, did not receive data");
        }

        $events = [];

        foreach ($data['data'] as $event) {
            if (!isset($event['type'], $event['payload']['id'], $event['created_at'])) {
                throw new Exception("Error in response, did not receive expected keys. Given keys: "
                                    . print_r(array_keys($event), true));
            }

            $date = Carbon::createFromFormat('Y-m-d H:i:s', $event['created_at']);
            $events[] = new UserEvent(
                eventType: $event['type'],
                userId: $event['payload']['id'],
                createdAt: $date instanceof Carbon ? $date : null
            );
        }

        return $events;
    }

    /**
     * @param UserInterface $user
     * @param string $errorType
     * @param string $errorMessage
     * @param array $additionalContext
     *
     * @return bool
     *
     * @throws Exception
     */
    public function notifyEmailSettingsError(
        UserInterface $user,
        string $errorType,
        string $errorMessage,
        array $additionalContext = [],
    ): bool
    {
        try {
            $gateway = $this->gatewayFactory->createForSystem();

            $eventData = [
                'type' => self::EVENT_TYPE_EMAIL_SETTINGS_ERROR,
                'user_id' => $user->getUserId(),
                'payload' => [
                    'error_type' => $errorType,
                    'user_friendly_message' => $this->createUserFriendlyMessage($errorMessage),
                    'technical_error' => $errorMessage,
                    'timestamp' => Carbon::now()->toISOString(),
                    'context' => $additionalContext,
                ]
            ];

            $response = $gateway->post(self::BASE_URL, $eventData);

            if ($response->getStatusCode() !== 200 && $response->getStatusCode() !== 201) {
                $this->logger->unknownError(
                    "Error while notifying Professional Works about email settings error: " . $response->getStatusCode(),
                    $response->getBody()->getContents()
                );

                return false;
            }

            $this->logger->info(
                'Successfully notified Professional Works about email settings error',
                ['user_id' => $user->getUserId(), 'error_type' => $errorType]
            );

            return true;
        } catch (Exception $e) {
            $this->logger->error(
                'Exception while notifying Professional Works about email settings fix: ' . $e->getMessage(),
                ['user_id' => $user->getUserId(), 'exception' => $e]
            );
            return false;
        }
    }

    private function createUserFriendlyMessage(string $technicalError): string
    {
        return match (true) {
            str_contains(strtolower($technicalError), 'authentication failed') ||
            str_contains(strtolower($technicalError), 'login failed') ||
            str_contains(strtolower($technicalError), 'invalid credentials') =>
            'Die Anmeldedaten (Benutzername/Passwort) sind nicht korrekt. Bitte überprüfen Sie Ihre Email-Einstellungen.',

            str_contains(strtolower($technicalError), 'connection refused') ||
            str_contains(strtolower($technicalError), 'could not connect') =>
            'Der Email-Server ist nicht erreichbar. Bitte prüfen Sie Host und Port in Ihren Email-Einstellungen.',

            str_contains(strtolower($technicalError), 'ssl') ||
            str_contains(strtolower($technicalError), 'tls') =>
            'Fehler bei der verschlüsselten Verbindung. Bitte prüfen Sie die SSL/TLS-Einstellungen.',

            str_contains(strtolower($technicalError), 'timeout') =>
            'Zeitüberschreitung bei der Verbindung zum Email-Server. Versuchen Sie es später erneut.',

            str_contains(strtolower($technicalError), 'smtp') =>
            'Fehler bei der SMTP-Verbindung (Email-Versand). Bitte überprüfen Sie Ihre SMTP-Einstellungen.',

            str_contains(strtolower($technicalError), 'imap') =>
            'Fehler bei der IMAP-Verbindung (Email-Empfang). Bitte überprüfen Sie Ihre IMAP-Einstellungen.',

            default => 'Ein Fehler ist bei der Email-Konfiguration aufgetreten. Bitte überprüfen Sie Ihre Email-Einstellungen oder kontaktieren Sie den Support.'
        };
    }
}
