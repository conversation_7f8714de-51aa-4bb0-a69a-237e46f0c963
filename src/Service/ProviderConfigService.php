<?php

declare(strict_types=1);

namespace App\Service;

use App\Email\Provider\ProviderConfigInterface;
use App\Entity\Email as EmailEntity;
use App\Entity\EmailHistoryError;
use App\Enum\ProviderConfigValidationError;
use App\Util\Result;
use Doctrine\ORM\EntityManagerInterface;

class ProviderConfigService
{
    private int $userId;
    private ProviderConfigInterface $providerConfig;

    public function __construct(private readonly EntityManagerInterface $em)
    {
    }

    /**
     * @return Result<null, ProviderConfigValidationError>
     */
    public function checkTransportForLimits(): Result
    {
        if ($this->isRateLimitReached()) {
            return Result::err(ProviderConfigValidationError::RATE_LIMIT_REACHED);
        }

        if ($this->isMaxConnectionErrorReached()) {
            return Result::err(ProviderConfigValidationError::MAX_CONNECTION_ERRORS_REACHED);
        }

        return Result::ok(null);
    }

    public function setUserId(int $userId): void
    {
        $this->userId = $userId;
    }

    public function setProviderConfig(ProviderConfigInterface $providerConfig): void
    {
        $this->providerConfig = $providerConfig;
    }

    private function isRateLimitReached(): bool
    {
        $emailEntityRepository = $this->em->getRepository(EmailEntity::class);
        $sentEmailCount = $emailEntityRepository->countEmailsSentCurrentHourByUser($this->userId);

        if ($sentEmailCount === null) {
            return false;
        }

        $maxCount = $this->providerConfig->getMaxSentEmailsPerUserPerTimespan();

        if ($maxCount === null) {
            return false;
        }

        return $sentEmailCount >= $maxCount->maxCount;
    }

    private function isMaxConnectionErrorReached(): bool
    {
        $emailHistoryErrorRepository = $this->em->getRepository(EmailHistoryError::class);
        $smtpErrorLoginCount = $emailHistoryErrorRepository->countFailedSmtpLogins($this->providerConfig->getDsn());

        $maxAllowedErrorCount = $this->providerConfig->getMaxSmtpConnectErrorsPerServerPerDay();

        if ($maxAllowedErrorCount === null) {
            return false;
        }

        return $smtpErrorLoginCount >= $maxAllowedErrorCount;
    }
}
