<?php

declare(strict_types=1);

namespace App\Service;

use App\Converter\AddressMimeConverter;
use App\Converter\EmailEntityConverter;
use App\Entity;
use App\Entity\Email as EmailEntity;
use App\Entity\SmtpImapConnection;
use App\Enum\ConnectionStatus;
use App\Enum\EmailStatus;
use App\Error\ProcessErrorType;
use App\Exception\EmailMimeConversionException;
use App\Exception\UnsupportedEmailStateException;
use App\Logging\EmailUuidProcessor;
use App\Logging\UserIdProcessor;
use App\Mailbox\ImapMailboxFactory;
use App\Message\SendEmailMessage;
use App\Repository\EmailSettingsRepository;
use App\Security\UserFactory;
use App\SendEmailStateMachine\SendEmailContext;
use App\SendEmailStateMachine\StatesWithTransitions\AbstractSendEmailState;
use App\SendEmailStateMachine\StatesWithTransitions\Posted;
use App\SendEmailStateMachine\StatesWithTransitions\RetryAt;
use App\SendEmailStateMachine\StatesWithTransitions\SendingFailed;
use App\SendEmailStateMachine\StatesWithTransitions\Sent;
use App\Transport\ParentFallbackTransportFactory;
use App\Transport\RateLimitedTransportInterface;
use App\Transport\TransportInterface;
use App\Util\EmailHistoryLogger;
use DateTimeImmutable;
use Doctrine\ORM\EntityManagerInterface;
use Sentry;
use Sentry\EventHint;
use Sentry\Severity;
use Symfony\Component\Console\Output\ConsoleOutput;
use Symfony\Component\Mailer\SentMessage;
use Symfony\Component\Messenger\MessageBusInterface;
use Symfony\Component\Messenger\Stamp\DelayStamp;
use Symfony\Component\Mime\Address;
use Symfony\Component\Mime\Email;
use Throwable;

use function Sentry\captureMessage;

class SendEmailService
{
    private const ONE_HOUR_AND_ONE_MINUTE_MS = 3660000;

    public function __construct(
        private readonly EntityManagerInterface $entityManager,
        private readonly EmailTransportService $emailTransportService,
        private readonly EmailEntityConverter $emailEntityConverter,
        private readonly EmailHistoryLogger $logger,
        private readonly Address $fallbackRecipient,
        private readonly EmailSettingsRepository $emailSettingsRepository,
        private readonly UserInfoServiceInterface $userInfoService,
        private readonly ParentFallbackTransportFactory $parentFallbackTransportFactory,
        private readonly MessageBusInterface $messageBus,
        private readonly ImapMailboxFactory $imapMailboxFactory,
    ) {
    }

    private function throwOnUnsupportedEmailStatus(EmailEntity $email): void
    {
        $supportedStates = [EmailStatus::QUEUED, EmailStatus::CREATED, EmailStatus::INITIALIZED];

        if (!in_array($email->getStatus(), $supportedStates, true)) {
            $message = 'only emails in the queued, created or initialized state can be sent';
            $this->logger->processError(type: ProcessErrorType::EMAIL_STATE_ERROR, cause: $message);

            throw new UnsupportedEmailStateException();
        }
    }

    private function convertToMimeEmail(EmailEntity $email, ?TransportInterface $transport): Email
    {
        $result = $this->emailEntityConverter->toMimeEmail($email);

        if ($result->isErr()) {
            foreach ($result->getError() as $validationErrorResult) {
                $this->logger->validationError($validationErrorResult, transport: (string)$transport);
            }

            throw new EmailMimeConversionException();
        }

        return $result->getValue();
    }

    public function handle(EmailEntity $email, ?ConsoleOutput $output = null): void
    {
        // Initialize
        $user = UserFactory::createForMailer($email->getUserId());

        UserIdProcessor::setUserId($user->getUserId());
        EmailUuidProcessor::setProcessedMailUuid($email->getUuid());

        $transport = $this->emailTransportService->getCheckAndUpdateTransportForUser($user);

        /** @var int $userId */
        $userId = $user->getUserId();
        /** @var int $emailId */
        $emailId = $email->getId();

        // Validate and check
        if (($transport instanceof RateLimitedTransportInterface) && $transport->hasReachedRateLimit($userId)) {
            // Todo: implement multi-rate-limit-handling
            // https://demvsystems.atlassian.net/browse/MAILER-573
            $delayStamp = new DelayStamp(self::ONE_HOUR_AND_ONE_MINUTE_MS);
            $this->messageBus->dispatch(new SendEmailMessage($emailId), [$delayStamp]);

            return;
        }

        $this->throwOnUnsupportedEmailStatus($email);
        $mimeEmail = $this->convertToMimeEmail($email, $transport);

        $context = new SendEmailContext(
            email: $mimeEmail,
            user: $user,
            userTransport: $transport,
            fallbackTransport: $this->emailTransportService->getFallbackTransport(),
            fallbackRecipient: $this->fallbackRecipient,
            useFailsafeTransport: $email->getUseFailsafeTransport(),
            emailSettings: $this->emailSettingsRepository->findByUser($user),
        );

        // Try sending the email
        try {
            $fromState = $email->getStatus();
            $email->setStatus(EmailStatus::SENDING);
            $this->entityManager->flush();

            $state = new Posted($context, $this->logger, $this->userInfoService, $this->parentFallbackTransportFactory);
            $postedCreatedState = $state::getIdentifier();

            $this->logger->transition($fromState->value, $postedCreatedState);

            // If there is a console output object, lets print some lines
            if ($output !== null) {
                $section = $output->section();
                $section->writeln(
                    sprintf(
                        "Sending Email (%d): '%s'",
                        $email->getId(),
                        substr($email->getSubject() ?? '', 0, 20)
                    )
                );
            }

            $state = $this->send($mimeEmail, $state);

            // Post-processing
            switch (true) {
                case $state instanceof Sent:
                    $sentMessage = $state->getSentMessage();

                    if ($sentMessage !== null) {
                        $this->updateEntityWithSentMessage($email, $sentMessage);
                    }

                    $email->setSentAt(new DateTimeImmutable());
                    $email->setStatus(EmailStatus::SENT);

                    if (
                        $context->emailSettings instanceof SmtpImapConnection
                        && $context->emailSettings->canReceive()
                        && $context->emailSettings->getReceivingStatus() === ConnectionStatus::ACTIVE
                        && $sentMessage !== null
                    ) {
                        $imapMailbox = $this->imapMailboxFactory->create($context->emailSettings);
                        $imapMailbox->saveEmailInSentFolder($sentMessage->getMessage()->toString());
                    }

                    break;
                case $state instanceof SendingFailed:
                    if (!$context->user->mayUseFailsafeTransport()) {
                        // if a user has no failsafe enabled we guess it wasn't supposed to fail and need to investigate
                        Sentry\captureMessage(
                            "Sending failed for email #{$email->getId()} and failsafe was not enabled",
                            Severity::error()
                        );
                    }

                    $email->setStatus(EmailStatus::SENDING_FAILED);

                    break;
                case $state instanceof RetryAt:
                    if ($context->getUser()->mayUseFailsafeTransport()) {
                        Sentry\captureMessage(
                            "Email #{$email->getId()} sent by user #{$context->getUser()->getUserId()} " .
                            "is marked for retry but only mails by system user should be resent automatically",
                            Severity::error()
                        );
                    }

                    if ($email->getRetryAt() === null) {
                        $email->setRetryAt($state->retryAt);
                        $this->logger->transition($email->getStatus()->value, EmailStatus::QUEUED->value);
                        $email->setStatus(EmailStatus::QUEUED);

                        $delay = ($state->retryAt->getTimestamp() - time()) * 1000;
                        $delayStamp = new DelayStamp($delay);
                        $this->messageBus->dispatch(new SendEmailMessage($emailId), [$delayStamp]);

                        break;
                    }

                    $this->logger->transition($email->getStatus()->value, EmailStatus::SENDING_FAILED->value);
                    $email->setStatus(EmailStatus::SENDING_FAILED);

                    break;
            }

            $this->entityManager->flush();

            return;
        } catch (Throwable $e) {
            $email->setStatus(EmailStatus::SENDING_FAILED);
            $this->entityManager->flush();

            $this->logger->unknownError($e->getMessage(), transport: (string)$transport);

            \Sentry\captureException($e);

            return;
        }
    }

    private function updateEntityWithSentMessage(EmailEntity $emailEntity, SentMessage $sentMessage): void
    {
        $emailEntity->setMessageId($sentMessage->getMessageId());

        $originalMessage = $sentMessage->getOriginalMessage();

        if (!($originalMessage instanceof Email)) {
            $sentryHint = new EventHint();
            captureMessage(
                message: "Original message in SentMessage was not of expected type " . Email::class . " but " .
                $originalMessage::class . ".",
                level: Severity::error(),
                hint: $sentryHint
            );

            return;
        }

        if (count($originalMessage->getFrom()) > 0) {
            $from = $originalMessage->getFrom()[0];
            $emailEntity->setActuallySentFromAddress(new Entity\Address($from->getAddress(), $from->getName()));
        }

        if (count($originalMessage->getReplyTo()) > 0) {
            $emailEntity->setReplyToAddress(AddressMimeConverter::toAddressEntity($originalMessage->getReplyTo()[0]));
        }
    }

    private function send(Email $mimeEmail, AbstractSendEmailState $state): AbstractSendEmailState
    {
        while (!$state->isFinal()) {
            $newState = $state->tryToSend($mimeEmail);
            $this->logger->transition($state::getIdentifier(), $newState::getIdentifier());
            $state = $newState;
        }

        return $state;
    }
}
