<?php

declare(strict_types=1);

namespace App\Service;

use App\Enum\ProviderConfigValidationError;
use App\Error\ValidationError;
use App\Error\ValidationErrorType;
use App\Transport\RateLimitedTransportInterface;
use App\Transport\TransportInterface;
use App\Util\EmailHistoryLogger;

readonly class RateLimitService
{
    public function __construct(private EmailHistoryLogger $emailHistoryLogger,)
    {
    }

    public function hasUserReachedRateLimitForTransport(
        RateLimitedTransportInterface&TransportInterface $transport,
        ?int $userId
    ): bool {
        if ($userId === null) {
            return false;
        }

        $result = $transport->checkProviderConfigForUser($userId);

        if ($result->isErr()) {
            $error = new ValidationError(
                ValidationErrorType::PROVIDER_CONFIG_CONSTRAINTS,
                $result->getError()->value
            );
            $this->emailHistoryLogger->validationError($error, (string)$transport);

            if ($result->getError() === ProviderConfigValidationError::RATE_LIMIT_REACHED) {
                return true;
            }
        }

        return false;
    }
}
