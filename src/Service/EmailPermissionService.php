<?php

declare(strict_types=1);

namespace App\Service;

use App\Error\AuthorizationErrorType;
use App\Security\User;
use App\Security\UserInterface;
use App\Security\UserStatus;
use App\Util\EmailHistoryLogger;
use Symfony\Bundle\SecurityBundle\Security;

class EmailPermissionService
{
    public function __construct(private readonly Security $security, private readonly EmailHistoryLogger $logger)
    {
    }

    /**
     * @param array<UserStatus> $forcedAllowedStatuses
     */
    public function hasPermission(UserInterface $user, array $forcedAllowedStatuses): bool
    {
        if (!$this->security->isGranted(USER::RIGHT_SEND_EMAIL)) {
            $this->logger->authorizationError(
                AuthorizationErrorType::USER_IS_TESTUSER,
                'Als Testbenutzer können Sie keine E-Mails versenden'
            );

            return false;
        }

        if (!in_array($user->getStatus(), [UserStatus::ACTIVE, ...$forcedAllowedStatuses], true)) {
            $this->logger->authorizationError(
                AuthorizationErrorType::USER_HAS_NOT_THE_CORRECT_STATUS,
                sprintf(
                    'Sie haben nicht den richtigen Status um diese Email zu versenden.'
                    . ' Ihr Status: %s - Benötigter Status: %s',
                    $user->getStatus()->value,
                    implode(
                        ',',
                        array_map(
                            static fn(UserStatus $status) => $status->value,
                            [UserStatus::ACTIVE, ...$forcedAllowedStatuses]
                        )
                    )
                )
            );

            return false;
        }

        return true;
    }
}
