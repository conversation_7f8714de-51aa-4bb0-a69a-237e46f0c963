<?php

declare(strict_types=1);

namespace App\MessageHandler;

use App\Message\PersistEmailMessage;
use App\Message\SendEmailMessage;
use App\Security\UserFactory;
use App\Service\EmailService;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;
use Symfony\Component\Messenger\MessageBusInterface;

#[AsMessageHandler]
readonly class PersistEmailMessageHandler
{
    public function __construct(private EmailService $emailService, private MessageBusInterface $messageBus)
    {
    }

    /**
     * @throws \Throwable
     */
    public function __invoke(PersistEmailMessage $emailMessage): void
    {
        $user = UserFactory::createForMailer($emailMessage->userId, $emailMessage->serviceId);
        $emailEntity = $this->emailService->createEmailEntityFromDto($emailMessage->emailDto, $user);

        /** @var int $emailId */
        $emailId = $emailEntity->getId();

        $this->messageBus->dispatch(new SendEmailMessage($emailId));
    }
}
