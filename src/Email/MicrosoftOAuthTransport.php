<?php

declare(strict_types=1);

namespace App\Email;

use App\Converter\EmailMimeConverter;
use App\Enum\ProviderConfigValidationError;
use App\OAuth\MicrosoftProvider;
use App\Transport\TransportInterface;
use App\Util\Result;
use Exception;
use Microsoft\Graph\Graph;
use Microsoft\Graph\Http\GraphResponse;
use Psr\EventDispatcher\EventDispatcherInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\Mailer\Envelope;
use Symfony\Component\Mailer\SentMessage;
use Symfony\Component\Mailer\Transport\AbstractApiTransport;
use Symfony\Component\Mime\Email;
use Symfony\Contracts\HttpClient\ResponseInterface;
use Throwable;

class MicrosoftOAuthTransport extends AbstractApiTransport implements TransportInterface
{
    private ?Graph $graph = null;

    public function __construct(
        private readonly MicrosoftProvider $microsoftProvider,
        private readonly string $scope,
        private readonly int $userId,
        ?EventDispatcherInterface $dispatcher = null,
        ?LoggerInterface $logger = null,
    ) {
        parent::__construct(null, $dispatcher, $logger);
    }

    protected function doSendApi(SentMessage $sentMessage, Email $email, Envelope $envelope): ResponseInterface
    {
        try {
            /** @var GraphResponse $response */
            $response = $this->getGraph()
                ->createRequest('POST', '/me/sendMail')
                ->addHeaders(['Content-Type' => 'application/json'])
                ->attachBody(['message' => EmailMimeConverter::toMicrosoftMessage($email)])
                ->execute();

            $responseStatus = (int) $response->getStatus();

            if ($responseStatus !== 202) {
                throw new Exception('Sending of message failed: ' . $response->getRawBody()?->read(100));
            }
        } catch (Throwable $exception) {
            \Sentry\captureException($exception);

            throw $exception;
        }

        return new MicrosoftResponse($response);
    }

    /**
     * @return Result<null, string>
     */
    public function testConnection(): Result
    {
        if (!in_array('https://graph.microsoft.com/Mail.Send', explode(' ', $this->scope), strict: true)) {
            return Result::err('The Mail.Send Scope is missing for the refresh-token');
        }

        try {
            /** @var GraphResponse $response */
            $response = $this->getGraph()->createRequest('GET', '/me')->execute();

            /** @phpstan-ignore-next-line The documentation of the Package seems to be wrong. */
            return $response->getStatus() === 200
                ? Result::ok(null)
                : Result::err((string) $response->getRawBody());
        } catch (Exception $exception) {
            return Result::err($exception->getMessage());
        }
    }

    private function getGraph(): Graph
    {
        if ($this->graph === null) {
            $this->graph = new Graph();
            $accessToken = $this->microsoftProvider->refreshAccessTokenForUserId($this->userId);

            if ($accessToken === null) {
                throw new Exception('No valid AccessToken available');
            }

            $this->graph->setAccessToken($accessToken);
        }

        return $this->graph;
    }

    /**
     * @return Result<null, ProviderConfigValidationError>
     */
    public function checkProviderConfigForUser(int $userId): Result
    {
        return Result::ok(null);
    }

    public function __toString(): string
    {
        return 'api://microsoft';
    }
}
