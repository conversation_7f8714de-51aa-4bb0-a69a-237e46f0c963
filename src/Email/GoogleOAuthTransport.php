<?php

declare(strict_types=1);

namespace App\Email;

use App\Converter\EmailMimeConverter;
use App\Enum\ProviderConfigValidationError;
use App\OAuth\GoogleProvider;
use App\Transport\TransportInterface;
use App\Util\Result;
use Exception;
use Google\Client as GoogleClient;
use Google\Service\Gmail;
use Google\Service\Gmail\Message;
use Google_Service_Gmail;
use Psr\EventDispatcher\EventDispatcherInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpClient\Response\MockResponse;
use Symfony\Component\Mailer\Envelope;
use Symfony\Component\Mailer\SentMessage;
use Symfony\Component\Mailer\Transport\AbstractApiTransport;
use Symfony\Component\Mime\Email;
use Symfony\Contracts\HttpClient\ResponseInterface;
use Throwable;

class GoogleOAuthTransport extends AbstractApiTransport implements TransportInterface
{
    /**
     * @link https://github.com/googleapis/google-api-php-client-services/blob/master/src/Google/Service/Gmail/Resource/UsersMessages.php#L196
     * The user's email address. The special value `me` can be
     * used to indicate the authenticated user.
     */
    private const GOOGLE_USER_ID_AUTHENTICATED_USER = 'me';

    private ?GoogleClient $googleClient = null;

    public function __construct(
        private readonly GoogleProvider $googleProvider,
        private readonly string $scope,
        private readonly int $userId,
        ?EventDispatcherInterface $dispatcher = null,
        ?LoggerInterface $logger = null,
    ) {
        parent::__construct(null, $dispatcher, $logger);
    }

    protected function doSendApi(SentMessage $sentMessage, Email $email, Envelope $envelope): ResponseInterface
    {
        $gmailService = new Gmail($this->getGoogleClient());

        $gMailMessage = new Message();
        $gMailMessage->setRaw(base64_encode(EmailMimeConverter::toStringWithBcc($email)));

        try {
            $gmailService->users_messages->send(self::GOOGLE_USER_ID_AUTHENTICATED_USER, $gMailMessage);
        } catch (Throwable $exception) {
            \Sentry\captureException($exception);

            throw $exception;
        }

        // Todo: figure out why message id is not being used anywhere, since we want so save it in the email entity
        // https://demvsystems.atlassian.net/browse/MAILER-555

        // As far as I can tell from debugging the response of the gmail service
        // there is no interesting information I could return except the messageId
        return new MockResponse();
    }

    /**
     * @return Result<null, string>
     */
    public function testConnection(): Result
    {
        if (!in_array(Google_Service_Gmail::GMAIL_SEND, explode(' ', $this->scope), strict: true)) {
            return Result::err('The gmail.send Scope is missing for the refresh-token');
        }

        try {
            $httpClient = $this->getGoogleClient()->authorize();

            $response = $httpClient->request('GET', 'https://www.googleapis.com/oauth2/v3/userinfo');

            return $response->getStatusCode() === 200
                ? Result::ok(null)
                : Result::err((string)$response->getBody());
        } catch (Exception $exception) {
            return Result::err($exception->getMessage());
        }
    }

    private function getGoogleClient(): GoogleClient
    {
        if ($this->googleClient === null) {
            $this->googleClient = $this->googleProvider->getCheckedClientForUserId($this->userId);
        }

        return $this->googleClient;
    }

    /**
     * @return Result<null, ProviderConfigValidationError>
     */
    public function checkProviderConfigForUser(int $userId): Result
    {
        return Result::ok(null);
    }

    public function __toString(): string
    {
        return 'api://google';
    }
}
