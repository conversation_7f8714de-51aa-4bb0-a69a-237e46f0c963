<?php

declare(strict_types=1);

namespace App\Email\Provider;

use Symfony\Component\Mailer\Transport\Dsn;

// DomainFactory is not a factory class but an actual provider
class DomainFactory implements ProviderConfigInterface
{
    public function getMaxSmtpConnectErrorsPerServerPerDay(): ?int
    {
        // No known limit
        return null;
    }

    public function getMaxSentEmailsPerKeepAlive(): int
    {
        // No known limit
        return 0;
    }

    public function getMaxSentEmailsPerUserPerTimespan(): ?MaxCountPerTimespan
    {
        return new MaxCountPerTimespan(60, 450);
    }

    public function supportsDsn(Dsn $dsn): bool
    {
        return $dsn->getHost() === 'df.eu' || str_ends_with($dsn->getHost(), '.df.eu');
    }

    public function getDsn(): string
    {
        return 'df.eu';
    }
}
