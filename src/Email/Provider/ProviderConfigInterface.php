<?php

declare(strict_types=1);

namespace App\Email\Provider;

use Symfony\Component\Mailer\Transport\Dsn;

interface ProviderConfigInterface
{
    /**
     * @description Um eine Sperre unserer IP bei den Providern zu umgehen,
     *   können wir eine Obergrenze an fehlgeschlagenen Verbindungsversuchen pro Server definieren.
     * Beispiel: Bei Strato führen zu viele Verbindungsversuche mit falschen Credentials
     *   innerhalb von 24h zu einer Sperre unsere IP-Adresse.
     *   Ein Nutzer kann damit den Mailversand von allen derzeit ~350 weiteren Strato Kunden lahmlegen.
     */
    public function getMaxSmtpConnectErrorsPerServerPerDay(): ?int;

    /**
     * @description Um in einem SMTP-KeepAlive Zyklus nicht zu viele E-Mails zu verschicken,
     *   können für die Provider eine Obergrenze an maximal versendeten Mails in einem KeepAlive definiert werden.
     * Beispiel: Bei IONOS führt der Versand via keep alive dazu, dass nur 20 mails pro session versendet werden können.
     *   Session wird nicht erneuert.
     */
    public function getMaxSentEmailsPerKeepAlive(): int;

    /**
     * @description Um eine Sperre der individuellen Zugangsdaten eines nutzers zu umgehen,
     *   können für die Provider eine Obergrenze an maximal versendeten Mails in einem Zeitraum definiert werden.
     * Beispiel: Bei IONOS (1&1) führt der Versand von vielen Mails (Serienbriefe) zur Sperre des Zugangs des Maklers.
     *   Genaues Limit ist nicht bekannt.
     *   Ausnahme per IP könnte wohl bei denen eingestellt werden, dazu brauchen wir allerdings eine dedizierte IP,
     *   welche die Makler auch nachhaltig per Ausnahme kommunizieren können
     */
    public function getMaxSentEmailsPerUserPerTimespan(): ?MaxCountPerTimespan;

    public function supportsDsn(Dsn $dsn): bool;

    public function getDsn(): string;
}
