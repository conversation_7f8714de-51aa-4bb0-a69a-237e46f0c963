<?php

declare(strict_types=1);

namespace App\Email\Provider;

use Symfony\Component\Mailer\Transport\Dsn;

class Ionos implements ProviderConfigInterface
{
    public function getMaxSmtpConnectErrorsPerServerPerDay(): ?int
    {
        // No known limit
        return null;
    }

    public function getMaxSentEmailsPerKeepAlive(): int
    {
        return 20;
    }

    public function getMaxSentEmailsPerUserPerTimespan(): ?MaxCountPerTimespan
    {
        return new MaxCountPerTimespan(60, 450);
    }

    public function supportsDsn(Dsn $dsn): bool
    {
        return $dsn->getHost() === 'ionos.de' || str_ends_with($dsn->getHost(), '.ionos.de');
    }

    public function getDsn(): string
    {
        return 'ionos.de';
    }
}
