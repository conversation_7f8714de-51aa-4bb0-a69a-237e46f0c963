<?php

declare(strict_types=1);

namespace App\Email\Provider;

use Symfony\Component\Mailer\Transport\Dsn;

class Strato implements ProviderConfigInterface
{
    public function getMaxSmtpConnectErrorsPerServerPerDay(): ?int
    {
        // The value is taken from PW, it is unclear if the value is appropriate
        return 20;
    }

    public function getMaxSentEmailsPerKeepAlive(): int
    {
        // No known limit
        return 0;
    }

    public function getMaxSentEmailsPerUserPerTimespan(): ?MaxCountPerTimespan
    {
        // No known limit
        return null;
    }

    public function supportsDsn(Dsn $dsn): bool
    {
        return $dsn->getHost() === 'strato.de' || str_ends_with($dsn->getHost(), '.strato.de');
    }

    public function getDsn(): string
    {
        return 'strato.de';
    }
}
