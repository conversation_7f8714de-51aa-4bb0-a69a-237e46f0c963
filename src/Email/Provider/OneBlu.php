<?php

declare(strict_types=1);

namespace App\Email\Provider;

use Symfony\Component\Mailer\Transport\Dsn;

class OneBlu implements ProviderConfigInterface
{
    public function getMaxSmtpConnectErrorsPerServerPerDay(): ?int
    {
        // No known limit
        return null;
    }

    public function getMaxSentEmailsPerKeepAlive(): int
    {
        // No known limit
        return 0;
    }

    public function getMaxSentEmailsPerUserPerTimespan(): ?MaxCountPerTimespan
    {
        return new MaxCountPerTimespan(timespanInMinutes: 60, maxCount: 145);
    }

    public function supportsDsn(Dsn $dsn): bool
    {
        return $dsn->getHost() === '1blu.de' || str_ends_with($dsn->getHost(), '.1blu.de');
    }

    public function getDsn(): string
    {
        return '1blu.de';
    }
}
