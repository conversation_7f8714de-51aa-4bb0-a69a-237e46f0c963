<?php

declare(strict_types=1);

namespace App\Email;

use App\Enum\EmailTransportScheme;
use App\Service\RateLimitService;
use App\Transport\TransportInterface;
use Psr\EventDispatcher\EventDispatcherInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\Mailer\Transport\AbstractTransportFactory;
use Symfony\Component\Mailer\Transport\Dsn;
use Symfony\Component\Mailer\Transport\Smtp\SmtpTransport;
use Symfony\Component\Mailer\Transport\TransportFactoryInterface;
use Symfony\Contracts\HttpClient\HttpClientInterface;

class SesSmtpTransportFactory extends AbstractTransportFactory
{
    public function __construct(
        private readonly TransportFactoryInterface $sesSmtpTransportFactory,
        private readonly RateLimitService $rateLimitService,
        EventDispatcherInterface $dispatcher = null,
        HttpClientInterface $client = null,
        LoggerInterface $logger = null,
    ) {
        parent::__construct($dispatcher, $client, $logger);
    }

    public function create(Dsn $dsn): TransportInterface
    {
        /** @var SmtpTransport $symfonySmtpTransport */
        $symfonySmtpTransport = $this->sesSmtpTransportFactory->create($dsn);

        return new SesSmtpTransport($symfonySmtpTransport, $this->rateLimitService);
    }

    /**
     * @return string[]
     */
    protected function getSupportedSchemes(): array
    {
        return [EmailTransportScheme::SES_SMTP->value, EmailTransportScheme::SES_SMTPS->value];
    }
}
