<?php

declare(strict_types=1);

namespace App\Email;

use App\Email\Provider\ProviderConfigInterface;
use App\Service\ProviderConfigService;
use App\Service\RateLimitService;
use App\Transport\RateLimitedTransportInterface;
use App\Transport\TransportInterface;
use App\Util\ExceptionHelper;
use App\Util\Result;
use Exception;
use Symfony\Component\Mailer\Envelope;
use Symfony\Component\Mailer\SentMessage;
use Symfony\Component\Mailer\Transport\Smtp\SmtpTransport as SymfonySmtpTransport;
use Symfony\Component\Mailer\Transport\Smtp\Stream\SocketStream;
use Symfony\Component\Mime\RawMessage;

class SmtpTransport implements TransportInterface, RateLimitedTransportInterface
{
    public function __construct(
        protected readonly SymfonySmtpTransport $smtpTransport,
        private readonly RateLimitService $rateLimitService,
        protected readonly ?ProviderConfigInterface $providerConfig = null,
        protected readonly ?ProviderConfigService $providerConfigService = null,
    ) {
    }

    /**
     * @return Result<null, string>
     */
    public function testConnection(): Result
    {
        // reduce the socket timeout when trying to establish (aka testing) a connection
        $this->setSocketTimeout(15);

        try {
            $this->smtpTransport->start();
        } catch (\Exception $e) {
            return Result::err('Test of smtp connection failed: ' . ExceptionHelper::getMessageChain($e));
        }

        return Result::ok(null);
    }

    public function send(RawMessage $message, Envelope $envelope = null): ?SentMessage
    {
        return $this->smtpTransport->send($message, $envelope);
    }

    public function setSocketTimeout(float $seconds, int $max = 60): void
    {
        $stream = $this->smtpTransport->getStream();

        if ($seconds <= 10.0) {
            throw new Exception('Remaining seconds for socket timeout are too few');
        }

        if ($stream instanceof SocketStream) {
            $stream->setTimeout(min($max, $seconds));
        }
    }

    public function checkProviderConfigForUser(int $userId): Result
    {
        // Wenn es keine Provider Config gibt, geben wir dennoch ein Ok zurück
        // da der Transport ohne ProviderConfig immer verwendet werden soll
        if ($this->providerConfig === null || $this->providerConfigService === null) {
            return Result::ok(null);
        }

        $this->providerConfigService->setUserId($userId);
        $this->providerConfigService->setProviderConfig($this->providerConfig);

        return $this->providerConfigService->checkTransportForLimits();
    }

    public function hasReachedRateLimit(?int $userId): bool
    {
        return $this->rateLimitService->hasUserReachedRateLimitForTransport($this, $userId);
    }

    public function __toString(): string
    {
        return (string)$this->smtpTransport;
    }
}
