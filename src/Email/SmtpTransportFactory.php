<?php

declare(strict_types=1);

namespace App\Email;

use App\Email\Provider\ProviderConfigInterface;
use App\Enum\EmailTransportScheme;
use App\Service\ProviderConfigService;
use App\Service\RateLimitService;
use App\Transport\TransportInterface;
use Psr\EventDispatcher\EventDispatcherInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\Mailer\Transport\AbstractTransportFactory;
use Symfony\Component\Mailer\Transport\Dsn;
use Symfony\Component\Mailer\Transport\Smtp\SmtpTransport as SymfonySmtpTransport;
use Symfony\Component\Mailer\Transport\TransportFactoryInterface;
use Symfony\Contracts\HttpClient\HttpClientInterface;

class SmtpTransportFactory extends AbstractTransportFactory
{
    /**
     * @param iterable<ProviderConfigInterface> $providerConfig
     */
    public function __construct(
        private readonly TransportFactoryInterface $esmtpTransportFactory,
        private readonly iterable $providerConfig,
        private readonly ProviderConfigService $providerConfigService,
        private readonly RateLimitService $rateLimitService,
        EventDispatcherInterface $dispatcher = null,
        HttpClientInterface $client = null,
        LoggerInterface $logger = null,
    ) {
        parent::__construct($dispatcher, $client, $logger);
    }

    public function create(Dsn $dsn): TransportInterface
    {
        /** @var SymfonySmtpTransport $symfonySmtpTransport */
        $symfonySmtpTransport = $this->esmtpTransportFactory->create($dsn);

        $providerConfig = $this->getProviderConfigForDsn($dsn);

        // Configure Transport for specific Provider
        if ($providerConfig !== null) {
            if (($restartThreshold = $providerConfig->getMaxSentEmailsPerKeepAlive()) !== 0) {
                $symfonySmtpTransport->setRestartThreshold($restartThreshold);
            }

            return new SmtpTransport(
                smtpTransport: $symfonySmtpTransport,
                rateLimitService: $this->rateLimitService,
                providerConfig: $providerConfig,
                providerConfigService: $this->providerConfigService,
            );
        }

        return new SmtpTransport($symfonySmtpTransport, $this->rateLimitService);
    }

    /**
     * @return string[]
     */
    protected function getSupportedSchemes(): array
    {
        return [
            EmailTransportScheme::SMTP->value,
            EmailTransportScheme::SMTPS->value,
        ];
    }

    private function getProviderConfigForDsn(Dsn $dsn): ?ProviderConfigInterface
    {
        foreach ($this->providerConfig as $provider) {
            if ($provider->supportsDsn($dsn)) {
                return $provider;
            }
        }

        return null;
    }
}
