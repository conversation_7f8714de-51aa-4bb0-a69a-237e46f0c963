<?php

declare(strict_types=1);

namespace App\Command;

use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(
    name: 'app:email-queue-worker',
    description: 'The command looks for emails to be sent asynchronously and sends them',
    hidden: false,
)]
class EmailQueueCommand extends Command
{
    // Todo: Delete this class after successful deployment of 2.0 (MAILER-599)
    public function __construct()
    {
        parent::__construct();
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $output->writeln([
            'Send queued emails',
            '============',
            '',
        ]);

        return Command::SUCCESS;
    }
}
