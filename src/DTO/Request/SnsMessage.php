<?php

declare(strict_types=1);

namespace App\DTO\Request;

use App\Enum\HashAlgorithm;
use App\Enum\SnsMessageType;
use App\Service\SignedMessageInterface;
use Symfony\Component\Validator\Constraints as Assert;

class SnsMessage implements SignedMessageInterface
{
    public function __construct(
        #[Assert\Choice(choices: [SnsMessageType::NOTIFICATION, SnsMessageType::SUBSCRIPTION_CONFIRMATION])]
        public readonly SnsMessageType $Type,
        public readonly string $MessageId,
        public readonly ?string $Token,
        public readonly string $TopicArn,
        public readonly ?string $Subject,
        public readonly string $Message,
        public readonly ?string $SubscribeURL,
        public readonly string $Timestamp,
        public readonly string $SignatureVersion,
        public readonly string $Signature,
        public readonly string $SigningCertURL,
        public readonly ?string $UnsubscribeURL,
    ) {
    }

    /**
     * This is not the MessageId of the bounced email!
     * To find that have a look at $this->Message->mail->messageId
     */
    public function getIdentifier(): string
    {
        return $this->MessageId;
    }

    public function getSignFormat(): string
    {
        $typeString = $this->Type->value;

        if ($this->Type === SnsMessageType::SUBSCRIPTION_CONFIRMATION) {
            return <<<EOT
                Message
                $this->Message
                MessageId
                $this->MessageId
                SubscribeURL
                $this->SubscribeURL
                Timestamp
                $this->Timestamp
                Token
                $this->Token
                TopicArn
                $this->TopicArn
                Type
                $typeString

                EOT;
        }

        // SnsMessageType::NOTIFICATION

        if ($this->Subject === null) {
            return <<<EOT
                    Message
                    $this->Message
                    MessageId
                    $this->MessageId
                    Timestamp
                    $this->Timestamp
                    TopicArn
                    $this->TopicArn
                    Type
                    $typeString

                    EOT;
        }

        return <<<EOT
                Message
                $this->Message
                MessageId
                $this->MessageId
                Subject
                $this->Subject
                Timestamp
                $this->Timestamp
                TopicArn
                $this->TopicArn
                Type
                $typeString

                EOT;
    }

    public function getCertificateURL(): string
    {
        return $this->SigningCertURL;
    }

    public function getSignature(): string
    {
        return $this->Signature;
    }

    public function getHashAlgorithm(): HashAlgorithm
    {
        return $this->SignatureVersion === '1' ? HashAlgorithm::SHA1 : HashAlgorithm::SHA256;
    }
}
