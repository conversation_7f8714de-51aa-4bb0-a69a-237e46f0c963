<?php

declare(strict_types=1);

namespace App\DTO\Request;

use OpenApi\Attributes as OA;
use Symfony\Component\Validator\Constraints as Assert;

#[OA\Schema(required: ['smtpHost', 'smtpPort', 'username', 'password'])]
readonly class SmtpImapConnection
{
    public function __construct(
        #[Assert\NotBlank]
        public string $smtpHost,
        public int $smtpPort,
        #[Assert\NotBlank]
        public string $username,
        #[Assert\NotBlank]
        public string $password,
        public ?string $imapHost,
        public ?int $imapPort,
    ) {
    }
}
