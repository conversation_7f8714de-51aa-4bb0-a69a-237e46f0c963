<?php

declare(strict_types=1);

namespace App\DTO\Request;

use App\Enum\ConnectionStatus;
use App\Enum\EmailTransportScheme;
use <PERSON><PERSON>\Serializer\Annotation\Type;
use <PERSON>Api\Attributes as OA;
use Symfony\Component\Validator\Constraints as Assert;

#[OA\Schema(required: ['email', 'scheme'])]
readonly class EmailSettings
{
    public function __construct(
        #[OA\Property(example: '<EMAIL>')]
        #[Assert\NotBlank]
        #[Assert\Email(message: 'The email {{ value }} is not a valid email.')]
        public string $email,
        #[Type('enum<' . EmailTransportScheme::class . '>')]
        public EmailTransportScheme $scheme,
        public ?string $senderName,
        // @phpstan-ignore argument.type
        #[OA\Property(type: 'choice', oneOf: [ConnectionStatus::ACTIVE, ConnectionStatus::INACTIVE])]
        #[Assert\Choice(choices: [ConnectionStatus::ACTIVE, ConnectionStatus::INACTIVE])]
        public ?ConnectionStatus $sendingStatus,
        // @phpstan-ignore argument.type
        #[OA\Property(type: 'choice', oneOf: [ConnectionStatus::ACTIVE, ConnectionStatus::INACTIVE])]
        #[Assert\Choice(choices: [ConnectionStatus::ACTIVE, ConnectionStatus::INACTIVE])]
        public ?ConnectionStatus $receivingStatus,
        #[Type('enum<' . SmtpImapConnection::class . '>')]
        public ?SmtpImapConnection $smtpImapConnection,
        #[Type('enum<' . OAuth2AuthConnection::class . '>')]
        public ?OAuth2AuthConnection $oAuthConnection,
    ) {
    }
}
