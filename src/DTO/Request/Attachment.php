<?php

declare(strict_types=1);

namespace App\DTO\Request;

use App\Enum\AttachmentContentDisposition;
use OpenApi\Attributes as OA;

class Attachment
{
    public function __construct(
        #[OA\Property(example: 'Contract.pdf')]
        public string $name,
        #[OA\Property(description: 'Must be base64 encoded')]
        public string $body, // is decoded during deserialization
        public ?string $contentType = null,
        #[OA\Property(default: 'attachment', enum: AttachmentContentDisposition::class)]
        public ?AttachmentContentDisposition $contentDisposition = AttachmentContentDisposition::ATTACHMENT,
        public ?string $contentId = null,
    ) {
    }
}
