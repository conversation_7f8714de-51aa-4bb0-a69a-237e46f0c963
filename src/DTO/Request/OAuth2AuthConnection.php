<?php

declare(strict_types=1);

namespace App\DTO\Request;

use DateTimeImmutable;
use OpenApi\Attributes as OA;
use Symfony\Component\Validator\Constraints as Assert;

#[OA\Schema(
    required: [
        'provider', 'access_token', 'refresh_token', 'expires_at', 'provider_user_id', 'provider_email',
    ]
)]
class OAuth2AuthConnection
{
    public function __construct(
        #[Assert\NotBlank]
        public readonly string $provider,
        #[Assert\NotBlank]
        public readonly string $access_token,
        #[Assert\NotBlank]
        public readonly string $refresh_token,
        public readonly DateTimeImmutable $expires_at,
        public readonly ?string $data,
        public readonly ?string $provider_user_id,
        public readonly ?string $provider_nickname,
        public readonly ?string $provider_name,
        #[Assert\NotBlank()]
        #[Assert\Email(message: 'The email {{ value }} is not a valid email.')]
        public readonly string $provider_email,
        public readonly ?string $provider_avatar,
    ) {
    }
}
