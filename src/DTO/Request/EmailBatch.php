<?php

declare(strict_types=1);

namespace App\DTO\Request;

use App\Security\UserStatus;
use <PERSON><PERSON>\Serializer\Annotation\Type;
use Nelmio\ApiDocBundle\Attribute\Model;
use OpenApi\Attributes as OA;
use Symfony\Component\Validator\Constraints as Assert;

#[OA\Schema(required: ['emails'])]
class EmailBatch
{
    #[OA\Property(description: "If the user email transport fails, send the email via the system email transport")]
    public bool $useFailsafeTransport = true;

    /** @var array<UserStatus> $alsoSendIfUserStatusIsIn */
    #[Type('array<enum<' . UserStatus::class . '>>')]
    #[Assert\All(new Assert\Type(UserStatus::class))]
    #[OA\Property(example: [UserStatus::PASSIVE, UserStatus::CANCELLED])]
    public array $alsoSendIfUserStatusIsIn;

    /** @var Email[] */
    #[OA\Property(type: 'array', items: new OA\Items(new Model(type: Email::class)))]
    public array $emails;

    public function __construct()
    {
        $this->alsoSendIfUserStatusIsIn = [];
        $this->emails = [];
    }
}
