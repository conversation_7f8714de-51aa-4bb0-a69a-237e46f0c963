<?php

declare(strict_types=1);

namespace App\DTO\Request;

use Nelmio\ApiDocBundle\Attribute\Model;
use OpenApi\Attributes as OA;
use Symfony\Component\Uid\Uuid;

#[OA\Schema(required: ['subject'])]
class Email
{
    #[OA\Property(description: <<<EOF
        The 'from' property in the Email represents the sender of the email.
        This property can only be set for application-generated emails.
        For user-generated emails, the sender is always the pre-configured email address.
        If a 'from' address is provided for user emails,
        the system will disregard it and use the pre-configured email address instead.
        EOF
    )]
    public ?Address $from = null;

    /** @var Address[] */
    #[OA\Property(type: 'array', items: new OA\Items(ref: new Model(type: Address::class)))]
    public array $to;

    /** @var Address[] */
    #[OA\Property(type: 'array', items: new OA\Items(ref: new Model(type: Address::class)))]
    public array $cc;

    /** @var Address[] */
    #[OA\Property(type: 'array', items: new OA\Items(ref: new Model(type: Address::class)))]
    public array $bcc;

    #[OA\Property(description: 'If system fallback transport has to be used user email address will be filled in here')]
    public ?Address $replyTo = null;

    #[OA\Property(example: "Email subject")]
    public string $subject;

    #[OA\Property(example: 'Hi! This is some email text')]
    public ?string $text = null;

    #[OA\Property(example: '<b>Hi!</b><i>This is some email text</i>')]
    public ?string $html = null;

    /** @var Attachment[] */
    #[OA\Property(type: 'array', items: new OA\Items(ref: new Model(type: Attachment::class)))]
    public array $attachments;

    #[OA\Property(
        description: <<<EOF
        The statusCallbackUrl property is a URL that will receive information about the email
        once the status of the email changes. The callbackUrl will be sent a POST request with the same Email object as
        it is returned by the Email Send API. Internal statuses are not published to the statusCallbackUrl.
        Internal statuses are: 'created', 'sending', 'initialized'.
        Also the queued is only published if an email gets into the status after it has already been processed before.
        EOF,
        example: 'https:://optional.api.domain/emails/a3e8caa6fcbd7bb4e37a1'
    )]
    public ?string $statusCallbackUrl = null;

    #[OA\Property(example: 'b24e37a1-6a6d-4f8d-a3e8-caa6fcbd7bb4')]
    public Uuid $uuid;

    #[OA\Property(description: 'Use system transport as fallback if user transport fails', default: true)]
    public bool $useFailsafeTransport = true;

    #[OA\Property(example: 'a3e8caa6fcbd7bb4e37a1')]
    public ?string $traceId = null;

    public function __construct(string $subject)
    {
        $this->to = [];
        $this->cc = [];
        $this->bcc = [];
        $this->attachments = [];
        $this->subject = $subject;
        $this->uuid = Uuid::v4();
    }
}
