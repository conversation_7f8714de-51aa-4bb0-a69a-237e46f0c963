<?php

declare(strict_types=1);

namespace App\DTO\Request;

use App\Security\UserStatus;
use <PERSON><PERSON>\Serializer\Annotation\Type;
use OpenApi\Attributes as OA;
use Symfony\Component\Validator\Constraints as Assert;

#[OA\Schema(required: ['emails'])]
readonly class SendEmailRequest
{
    public function __construct(
        public Email $email,
        #[OA\Property(description: "If the user email transport fails, send the email via the system email transport")]
        public bool $useFailsafeTransport = true,
        /** @var array<UserStatus> $alsoSendIfUserStatusIsIn */
        #[OA\Property(
            description: "Emails are only sent if the User is \"active\". " .
            "By providing this array, the emails are also sent if the user status is in the array.",
        )]
        #[Type('array<enum<' . UserStatus::class . '>>')]
        #[Assert\All(new Assert\Type(UserStatus::class))]
        public array $alsoSendIfUserStatusIsIn = [],
    ) {
    }
}
