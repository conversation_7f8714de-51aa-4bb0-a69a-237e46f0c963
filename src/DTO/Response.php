<?php

declare(strict_types=1);

namespace App\DTO;

use App\Enum\ResponseStatus;
use JsonSerializable;
use OpenApi\Attributes as OA;

class Response implements JsonSerializable
{
    private mixed $response;

    private mixed $error;

    private function __construct(
        #[OA\Property(type: 'enum', enum: [ResponseStatus::Success, ResponseStatus::Error])]
        public readonly ResponseStatus $status,
        mixed $value
    ) {
        match ($status) {
            ResponseStatus::Success => $this->response = $value,
            ResponseStatus::Error => $this->error = $value,
        };
    }

    public static function success(mixed $value = null): self
    {
        return new self(ResponseStatus::Success, $value);
    }

    public static function error(mixed $value = null): self
    {
        return new self(ResponseStatus::Error, $value);
    }

    public function getResponse(): mixed
    {
        return $this->response;
    }

    public function getError(): mixed
    {
        return $this->error;
    }

    /**
     * @return array<string, mixed>
     */
    public function jsonSerialize(): array
    {
        return match ($this->status) {
            ResponseStatus::Error => ['status' => $this->status, 'error' => $this->error],
            ResponseStatus::Success => ['status' => $this->status, 'response' => $this->response]
        };
    }
}
