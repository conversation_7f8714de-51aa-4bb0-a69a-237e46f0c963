<?php

declare(strict_types=1);

namespace App\DTO\Response;

readonly class ApiErrorCollectionDto
{
    /**
     * @param \App\DTO\Response\ApiErrorDto[] $errors
     */
    public function __construct(public array $errors,)
    {
    }

    /**
     * @param array<int,string|\App\DTO\Response\ApiErrorDto> $errors
     */
    public static function withErrors(array $errors): self
    {
        $errorArray = [];

        foreach ($errors as $error) {
            switch (true) {
                case is_string($error):
                    $error = new ApiErrorDto($error);
                    // no break, from here we handle the ApiErrorDto on the next case
                case $error instanceof ApiErrorDto:
                    $errorArray[] = $error;

                    break;
                default:
                    throw new \TypeError('Errors must either be strings or instances of ApiErrorDto');
            }
        }

        return new self($errorArray);
    }
}
