<?php

declare(strict_types=1);

namespace App\DTO\Response;

use App\Enum\ConnectionStatus;
use App\Enum\EmailTransportScheme;
use OpenApi\Attributes as OA;

#[OA\Schema(required: ['userId', 'email', 'scheme'])]
readonly class EmailSettings
{
    public function __construct(
        public int $userId,
        public string $email,
        public EmailTransportScheme $scheme,
        public ?string $senderName,
        public ?ConnectionStatus $sendingStatus = null,
        public ?ConnectionStatus $receivingStatus = null,
        public ?OAuth2AuthConnection $oAuth2AuthConnection = null,
        public ?SmtpImapConnection $smtpImapConnection = null,
    ) {
    }
}
