<?php

declare(strict_types=1);

namespace App\DTO\Response;

use App\Enum\EmailStatus;
use DateTimeImmutable;
use Nelmio\ApiDocBundle\Attribute\Model;
use OpenApi\Attributes as OA;

readonly class EmailSent implements ResponseDtoInterface
{
    /**
     * @param Address[] $toAddress
     * @param Address[] $ccAddress
     * @param Address[] $bccAddress
     * @param Attachment[] $attachments
     * @param Error[] $errors
     */
    public function __construct(
        public int $id,
        public string $uuid,
        public ?int $userId,
        public string $subject,
        public EmailStatus $status,
        public ?Address $fromAddress = null,
        public ?Address $actuallySentFrom = null,
        public ?Address $replyTo = null,
        #[OA\Property(type: 'array', items: new OA\Items(ref: new Model(type: Address::class)))]
        public array $toAddress = [],
        #[OA\Property(type: 'array', items: new OA\Items(ref: new Model(type: Address::class)))]
        public array $ccAddress = [],
        #[OA\Property(type: 'array', items: new OA\Items(ref: new Model(type: Address::class)))]
        public array $bccAddress = [],
        public ?string $bodyHtml = null,
        public ?string $bodyText = null,
        #[OA\Property(type: 'array', items: new OA\Items(ref: new Model(type: Attachment::class)))]
        public ?array $attachments = null,
        public ?DateTimeImmutable $sentAt = null,
        public ?DateTimeImmutable $createdAt = null,
        #[OA\Property(
            type: 'array',
            items: new OA\Items(ref: new Model(type: Error::class)),
            example: [new Error('string', 'string', '?string', '?string')]
        )]
        public array $errors = [],
    ) {
    }
}
