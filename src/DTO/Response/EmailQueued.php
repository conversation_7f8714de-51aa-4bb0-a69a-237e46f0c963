<?php

declare(strict_types=1);

namespace App\DTO\Response;

use Nelmio\ApiDocBundle\Attribute\Model;
use OpenApi\Attributes as OA;
use Symfony\Component\Uid\Uuid;

readonly class EmailQueued implements ResponseDtoInterface
{
    /**
     * @param \App\DTO\Request\Address[] $to
     * TODO: add SHA256 email hash (MAILER-600)
     */
    public function __construct(
        #[OA\Property(example: 'b24e37a1-6a6d-4f8d-a3e8-caa6fcbd7bb4')]
        public Uuid $uuid,
        #[OA\Property(type: 'array', items: new OA\Items(new Model(type: Address::class)))]
        public array $to,
        #[OA\Property(example: 'Test subject')]
        public string $subject,
        #[OA\Property(example: 'a3e8caa6fcbd7bb4e37a1')]
        public ?string $traceId,
    ) {
    }
}
