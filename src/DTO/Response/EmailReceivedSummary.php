<?php

declare(strict_types=1);

namespace App\DTO\Response;

use Nelmio\ApiDocBundle\Attribute\Model;
use OpenApi\Attributes as OA;

readonly class EmailReceivedSummary
{
    /**
     * @param array<Address> $to
     * @param array<Address> $cc
     * @param array<Address> $bcc
     */
    public function __construct(
        #[OA\Property(example: 'RE: DEM-12345 Risikovoranfrage Daniel Düsentrieb')]
        public string $subject,
        #[OA\Property(example: 1682454708)]
        public int $timestamp,
        #[OA\Property(example: '1690285127:5')]
        public string $uid,
        public ?Address $from = null,
        #[OA\Property(type: 'array', items: new OA\Items(ref: new Model(type: Address::class)))]
        public array $to = [],
        #[OA\Property(type: 'array', items: new OA\Items(ref: new Model(type: Address::class)))]
        public array $cc = [],
        #[OA\Property(type: 'array', items: new OA\Items(ref: new Model(type: Address::class)))]
        public array $bcc = [],
        #[OA\Property(example: '<EMAIL>')]
        public ?string $message_id = null,
    ) {
    }
}
