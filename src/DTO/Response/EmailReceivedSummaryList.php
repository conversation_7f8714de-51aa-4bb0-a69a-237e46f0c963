<?php

declare(strict_types=1);

namespace App\DTO\Response;

use Nelmio\ApiDocBundle\Attribute\Model;
use OpenApi\Attributes as OA;

readonly class EmailReceivedSummaryList
{
    /**
     * @param EmailReceivedSummary[] $emails
     */
    public function __construct(
        #[OA\Property(type: 'array', items: new OA\Items(ref: new Model(type: EmailReceivedSummary::class)))]
        public array $emails,
    ) {
    }
}
