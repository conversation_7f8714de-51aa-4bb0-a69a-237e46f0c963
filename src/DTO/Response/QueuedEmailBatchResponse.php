<?php

declare(strict_types=1);

namespace App\DTO\Response;

use Nelmio\ApiDocBundle\Attribute\Model;
use OpenApi\Attributes as OA;

readonly class QueuedEmailBatchResponse
{
    /**
     * @param EmailQueued[] $queuedEmails
     * @param array<int, array{reason:string, email:EmailQueued}> $skippedEmails
     */
    public function __construct(
        #[OA\Property(description: 'This field will always return 0 and will be removed in the futurue', example: 0)]
        public int $sentEmailCount,
        #[OA\Property(example: 1)]
        public int $queuedEmailCount,
        #[OA\Property(example: 3)]
        public int $skippedEmailCount,
        #[OA\Property(type: 'array', items: new OA\Items(new Model(type: EmailQueued::class)))]
        public array $queuedEmails,
        #[OA\Property(properties: ([
            new OA\Property(property: 'reason', type: 'string', example: 'Email size exceeds 20MB'),
            new OA\Property(
                property: 'email',
                type: 'array',
                items: new OA\Items(new Model(type: EmailQueued::class))
            ),
        ]), type: 'object')]
        public array $skippedEmails,
    ) {
    }
}
