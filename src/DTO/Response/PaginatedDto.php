<?php

declare(strict_types=1);

namespace App\DTO\Response;

use Nelmio\ApiDocBundle\Attribute\Model;
use OpenApi\Attributes as OA;

readonly class PaginatedDto
{
    /**
     * @param ResponseDtoInterface[] $resources
     */
    public function __construct(
        public int $total,
        public ?int $firstEmailId,
        public ?int $lastEmailId,
        #[OA\Property(type: 'array', items: new OA\Items(new Model(type: EmailSent::class)))]
        public array $resources,
    ) {
    }
}
