<?php

declare(strict_types=1);

namespace App\DTO\Response;

use Nelmio\ApiDocBundle\Attribute\Model;
use OpenApi\Attributes as OA;

readonly class EmailReceived
{
    /**
     * @param array<Address> $to
     * @param array<Address> $cc
     * @param array<Address> $bcc
     */
    public function __construct(
        public ?string $uid,
        public ?string $subject,
        public ?string $date,
        public ?string $mailboxFolder,
        public ?string $mimeVersion,
        public ?string $contentType,
        public ?string $fromHost,
        public ?string $fromName,
        public ?string $fromAddress,
        public ?string $senderHost,
        public ?string $senderName,
        public ?string $senderAddress,
        public ?string $xOriginalTo,
        public ?string $messageId,
        #[OA\Property(type: 'array', items: new OA\Items(ref: new Model(type: Address::class)))]
        public array $to = [],
        #[OA\Property(type: 'array', items: new OA\Items(ref: new Model(type: Address::class)))]
        public array $cc = [],
        #[OA\Property(type: 'array', items: new OA\Items(ref: new Model(type: Address::class)))]
        public array $bcc = [],
    ) {
    }
}
