<?php

declare(strict_types=1);

namespace App\DTO\Response;

use DateTimeImmutable;
use OpenApi\Attributes as OA;

#[OA\Schema(
    required: [
        'email',
        'provider',
        'access_token',
        'refresh_token',
        'expires_at',
        'provider_user_id',
        'provider_email',
    ]
)]
readonly class OAuth2AuthConnection
{
    public function __construct(
        public string $provider,
        public ?DateTimeImmutable $expires_at,
        public ?string $data,
        public ?string $provider_user_id,
        public ?string $provider_nickname,
        public ?string $provider_name,
        public ?string $provider_email,
        public ?string $provider_avatar,
    ) {
    }
}
