<?php

declare(strict_types=1);

namespace App\SendEmailStateMachine\StatesWithTransitions;

use App\Error\TransportError;
use App\Error\TransportErrorType;
use App\Logging\ErrorLogContext;
use App\Security\SystemUser;
use App\SendEmailStateMachine\SendEmailContext;
use App\Service\UserInfoServiceInterface;
use App\Transport\ParentFallbackTransportFactory;
use Carbon\CarbonImmutable;
use Psr\Log\LoggerInterface;
use Symfony\Component\Mime\Address;
use Symfony\Component\Mime\Email;
use Throwable;

class Posted extends AbstractSendEmailState
{
    public function __construct(
        private readonly SendEmailContext $context,
        private readonly LoggerInterface $logger,
        private readonly UserInfoServiceInterface $userInfoService,
        private readonly ParentFallbackTransportFactory $parentFallbackTransportFactory,
    ) {
    }

    public static function getIdentifier(): string
    {
        return 'Posted';
    }

    /**
     * @codeCoverageIgnore
     */
    public function isFinal(): bool
    {
        return false;
    }

    protected function prepare(Email $email): Email
    {
        return $this->ensureFromHeader($email);
    }

    public function tryToSend(Email $email): AbstractSendEmailState
    {
        $email = $this->prepare($email);

        $transport = $this->context->getUserTransport();

        if ($transport === null) {
            $err = new TransportError(TransportErrorType::NO_TRANSPORT_AVAILABLE, 'User has no valid transport');
            $this->logger->error('', context: [ErrorLogContext::IDENTIFIER => new ErrorLogContext($err)]);

            return $this->handleTransportError($err);
        }

        try {
            return new Sent($transport->send($email));
        } catch (Throwable $e) {
            return $this->handleTransportError(TransportError::fromSmtpErrorMessage($e->getMessage()));
        }
    }

    private function handleTransportError(TransportError $err): AbstractSendEmailState
    {
        return match ($err->getType()) {
            TransportErrorType::ATTACHMENT_TOO_LARGE => new AttachmentTooLarge($this->context),
            TransportErrorType::TIMEOUT_SES => new RetryAt((new CarbonImmutable())->addMinutes(3)),
            default => new UserTransportFailedOrNull($this->context, $this->parentFallbackTransportFactory),
        };
    }

    /**
     * If from header was missing, try to create a fallback from the email settings.
     * If email settings are missing as well, try to retrieve the email address from PW.
     */
    private function ensureFromHeader(Email $mimeEmail): Email
    {
        if ($mimeEmail->getFrom() === []) {
            if ($this->context->user instanceof SystemUser) {
                // TODO MAILER-19 Log if this case occurs
            }

            if ($this->context->emailSettings === null || $this->context->emailSettings->getEmail() === '') {
                $replyToAddress = $this->userInfoService->getUserInfo($this->context->user)->email;
                // When the email is sent with the fallback transport the from address is replaced by the
                // SenderAdjustingTransport and this from address is used as reply to address.
                $mimeEmail->from($replyToAddress);
            } else {
                $mimeEmail->from(
                    new Address(
                        $this->context->emailSettings->getEmail(),
                        $this->context->emailSettings->getSenderName() ?? ''
                    )
                );
            }
        }

        return $mimeEmail;
    }
}
