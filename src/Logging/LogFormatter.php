<?php

declare(strict_types=1);

namespace App\Logging;

use Monolog\Formatter\JsonFormatter;
use Monolog\LogRecord;

class LogFormatter extends JsonFormatter
{
    public function normalizeRecord(LogRecord $record): array
    {
        $normalized = parent::normalizeRecord($record);
        /** @var array<string, mixed> $extra */
        $extra = $normalized['extra'];
        $normalized['user_id'] = $extra['user_id'] ?? null;
        $normalized['level'] = $record->level->toRFC5424Level();

        return $normalized;
    }
}
