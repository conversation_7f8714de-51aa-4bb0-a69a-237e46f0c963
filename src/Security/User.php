<?php

declare(strict_types=1);

namespace App\Security;

use Lexik\Bundle\JWTAuthenticationBundle\Security\User\JWTUserInterface;

abstract class User implements JWTUserInterface, UserInterface
{
    public const ROLE_USER = 'ROLE_USER';
    public const ROLE_SYSTEM = 'ROLE_SYSTEM';
    public const ROLE_ADMIN = 'ROLE_ADMIN';

    public const RIGHT_SEND_EMAIL = 'emails_versenden';

    protected const KEY_ADMIN = 'admin';

    /** @param array<string> $rights */
    public function __construct(public readonly array $rights = [])
    {
    }

    /**
     * @param array<mixed> $payload
     */
    public static function createFromPayload($username, array $payload): JWTUserInterface
    {
        return match (true) {
            array_key_exists(PwUser::KEY_USER_ID, $payload) => PwUser::create(serviceId: $username, payload: $payload),
            default => SystemUser::create(serviceId: $username, payload: $payload)
        };
    }

    public function eraseCredentials(): void
    {
    }

    /**
     * @return array<string>
     */
    public function getRights(): array
    {
        return $this->rights;
    }
}
