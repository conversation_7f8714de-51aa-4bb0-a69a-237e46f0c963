<?php

declare(strict_types=1);

namespace App\Security;

class UserFactory
{
    /**
     * This factory is used to create user instances for async processes like commands
     */
    public static function createForMailer(?int $userId, string $serviceId = 'mailer'): UserInterface
    {
        return $userId !== null
            ? PwUser::createForMailer($userId, $serviceId)
            : SystemUser::create($serviceId, ['admin' => false]);
    }
}
