<?php

declare(strict_types=1);

namespace App\Transport\Decorator;

use App\Transport\TransportInterface;
use App\Util\Result;

abstract class AbstractTransportDecorator implements TransportInterface
{
    public function __construct(readonly protected TransportInterface $inner)
    {
    }

    public function testConnection(): Result
    {
        return $this->inner->testConnection();
    }

    public function checkProviderConfigForUser(int $userId): Result
    {
        return $this->inner->checkProviderConfigForUser($userId);
    }
}
