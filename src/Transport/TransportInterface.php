<?php

declare(strict_types=1);

namespace App\Transport;

use App\Email\ConnectionTestInterface;
use App\Enum\ProviderConfigValidationError;
use App\Util\Result;
use Symfony\Component\Mailer\Transport\TransportInterface as SymfonyTransportInterface;

interface TransportInterface extends
    SymfonyTransportInterface,
    ConnectionTestInterface
{
    /**
     * @return Result<null, ProviderConfigValidationError>
     */
    public function checkProviderConfigForUser(int $userId): Result;
}
