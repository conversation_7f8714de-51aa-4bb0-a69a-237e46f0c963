<?php

declare(strict_types=1);

namespace App\Transport;

use App\Email\ConnectionTestInterface;
use App\Enum\ProviderConfigValidationError;
use App\Util\Result;
use Symfony\Component\Mailer\Envelope;
use <PERSON>ymfony\Component\Mailer\SentMessage;
use Symfony\Component\Mailer\Transport\TransportInterface as SymfonyTransportInterface;
use Symfony\Component\Mime\RawMessage;

class SymfonyTransportAdapter implements TransportInterface
{
    public function __construct(private SymfonyTransportInterface $inner)
    {
    }

    public function send(RawMessage $message, ?Envelope $envelope = null): ?SentMessage
    {
        return $this->inner->send($message, $envelope);
    }

    public function testConnection(): Result
    {
        return Result::err(ConnectionTestInterface::ERROR_MSG_CONNECTION_TEST_NOT_SUPPORTED);
    }

    /**
     * @return Result<null, ProviderConfigValidationError>
     */
    public function checkProviderConfigForUser(int $userId): Result
    {
        return Result::ok(null);
    }

    public function __toString(): string
    {
        return (string) $this->inner;
    }
}
