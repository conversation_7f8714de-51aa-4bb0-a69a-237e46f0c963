<?php

declare(strict_types=1);

namespace App\Error;

readonly class TransportError implements ErrorInterface
{
    private const INDICATOR_SUBSTRINGS = [
        ['Refused by local policy. Sending of SPAM is not permitted!', TransportErrorType::CLASSIFIED_AS_SPAM],
        ['500 Message size limit exceeded', TransportErrorType::ATTACHMENT_TOO_LARGE],
        ['552 Mail size limit exceeded', TransportErrorType::ATTACHMENT_TOO_LARGE],
        ['451 4.4.2 Timeout waiting for data from client', TransportErrorType::TIMEOUT_SES],
    ];

    public function __construct(private TransportErrorType $type, private ?string $cause = null)
    {
    }

    public static function fromSmtpErrorMessage(string $msg): self
    {
        foreach (self::INDICATOR_SUBSTRINGS as [$substring, $type]) {
            if (str_contains($msg, $substring)) {
                return new self($type, $msg);
            }
        }

        return new TransportError(TransportErrorType::UNKNOWN, $msg);
    }

    public function getKind(): string
    {
        return 'TransportError';
    }

    public function getType(): TransportErrorType
    {
        return $this->type;
    }

    public function getTypeString(): string
    {
        return $this->type->value;
    }

    public function getCause(): ?string
    {
        return $this->cause;
    }

    public function jsonSerialize(): array
    {
        return [
            "kind" => $this->getKind(),
            "type" => $this->getTypeString(),
            "cause" => $this->getCause(),
        ];
    }
}
