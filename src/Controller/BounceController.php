<?php

declare(strict_types=1);

namespace App\Controller;

use App\DTO\Request\SnsMessage;
use App\Entity\Email;
use App\Enum\EmailStatus;
use App\Enum\SnsMessageNotificationType;
use App\Enum\SnsMessageType;
use App\Error\BounceError;
use App\Error\BounceErrorType;
use App\Error\ComplaintError;
use App\Error\ComplaintErrorType;
use App\Error\ValidationError;
use App\Error\ValidationErrorType;
use App\Logging\EmailUuidProcessor;
use App\Logging\ErrorLogContext;
use App\Logging\TransitionLogContext;
use App\Repository\EmailRepository;
use App\Service\SignatureService;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\EntityNotFoundException;
use Nelmio\ApiDocBundle\Attribute\Model;
use OpenApi\Attributes as OA;
use Psr\Log\LoggerInterface;
use stdClass;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Attribute\MapRequestPayload;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Contracts\HttpClient\HttpClientInterface;

use function Sentry\captureException;

class BounceController extends AbstractController
{
    public function __construct(
        private readonly SignatureService $signatureService,
        private readonly HttpClientInterface $httpClient,
        private readonly LoggerInterface $logger,
        private readonly EmailRepository $emailRepository,
        private readonly EntityManagerInterface $em
    ) {
    }

    #[OA\Post(path: '/v1/bounces/ses', description: 'Receives bounces from Amazon SES', summary: '/v1/bounces/ses')]
    #[OA\Response(response: 200, description: 'Bounce was received and persisted')]
    #[OA\Response(response: 202, description: 'Subscription confirmed')]
    #[OA\Response(response: 400, description: "Request is not properly formatted")]
    #[OA\RequestBody(description: 'Get work times by filter', content: new Model(type: SnsMessage::class))]
    #[Route('/v1/bounces/ses', name: 'bounces', methods: ['POST'], format: 'json')]
    public function receiveSeSBounces(#[MapRequestPayload] SnsMessage $snsMessage): Response
    {
        $validationResult = $this->signatureService->validate($snsMessage);

        if ($validationResult->isErr()) {
            $error = new ValidationError(ValidationErrorType::SIGNATURE_INVALID, $validationResult->getError());
            $context = new ErrorLogContext($error);
            $this->logger->error('', [ErrorLogContext::IDENTIFIER => $context]);
            $this->em->flush();

            return new Response(status: Response::HTTP_BAD_REQUEST);
        }

        return match ($snsMessage->Type) {
            SnsMessageType::NOTIFICATION => $this->handleNotification($snsMessage),
            SnsMessageType::SUBSCRIPTION_CONFIRMATION => $this->handleConfirmation($snsMessage),
        };
    }

    private function handleNotification(SnsMessage $snsMessage): Response
    {
        /** @var stdClass $content */
        $content = json_decode($snsMessage->Message);

        return match ($content->notificationType) {
            SnsMessageNotificationType::BOUNCE->value => $this->handleBounce($content, $snsMessage),
            SnsMessageNotificationType::COMPLAINT->value => $this->handleComplaint($content, $snsMessage),
            SnsMessageNotificationType::DELIVERY->value => (static function () {
                captureException(
                    new \LogicException(
                        'Received delivery notification even though we do not want to subscribe to them',
                    )
                );

                return new Response(status: Response::HTTP_OK);
            })(),
            default => throw new \InvalidArgumentException('Unknown notification type'),
        };
    }

    private function handleBounce(stdClass $content, SnsMessage $snsMessage): Response
    {
        $bounce = $content->bounce;
        $messageId = $content->mail->messageId . '@eu-central-1.amazonses.com';
        $encodedBouncedRecipients = json_encode($bounce->bouncedRecipients);
        $cause = is_string($encodedBouncedRecipients) ? $encodedBouncedRecipients : 'unknown';
        $debugInfo = null;

        $emailEntity = $this->emailRepository->getByMessageId($messageId);

        if ($emailEntity === null) {
            captureException(
                new EntityNotFoundException(
                    'Entity of type ' . Email::class . ' with messageId ' . $messageId . ' was not found.',
                )
            );

            $encoded = json_encode($snsMessage);

            $debugInfo = $encoded !== false ? $encoded : '';
        }

        $this->logBounceAndAdjustStatus(
            BounceErrorType::from(strtolower($bounce->bounceType)),
            $cause,
            $debugInfo,
            $emailEntity
        );

        return new Response(status: Response::HTTP_OK);
    }

    private function handleComplaint(stdClass $content, SnsMessage $snsMessage): Response
    {
        $complaint = $content->complaint;
        $messageId = $content->mail->messageId . '@eu-central-1.amazonses.com';
        $debugInfo = null;

        $emailEntity = $this->emailRepository->getByMessageId($messageId);

        if ($emailEntity === null) {
            captureException(
                new EntityNotFoundException(
                    'Entity of type ' . Email::class . ' with messageId ' . $messageId . ' was not found.',
                )
            );

            $encoded = json_encode($snsMessage);

            $debugInfo = $encoded !== false ? $encoded : '';
        }

        $encodedBouncedRecipients = json_encode($complaint->complainedRecipients);
        $cause = is_string($encodedBouncedRecipients) ? $encodedBouncedRecipients : 'unknown';
        $type = ComplaintErrorType::tryFrom($complaint->complaintSubType ?? '') ?? ComplaintErrorType::UNDETERMINED;

        $this->logComplaintAndAdjustStatus($type, $cause, $debugInfo, $emailEntity);

        return new Response(status: Response::HTTP_OK);
    }

    private function handleConfirmation(SnsMessage $snsMessage): Response
    {
        $confirmSubscriptionUrl = $snsMessage->SubscribeURL;

        if ($confirmSubscriptionUrl === null) {
            return new Response(status: Response::HTTP_BAD_REQUEST);
        }

        $response = $this->httpClient->request('GET', $confirmSubscriptionUrl);

        $this->logger->info($response->getContent());

        return new Response($response->getContent(), status: Response::HTTP_ACCEPTED);
    }

    private function logBounceAndAdjustStatus(
        BounceErrorType $bounceType,
        ?string $cause = null,
        ?string $debugInfo = null,
        ?Email $email = null,
    ): void {
        if ($email !== null) {
            EmailUuidProcessor::setProcessedMailUuid($email->getUuid());
        }

        $error = new BounceError($bounceType, $cause);
        $context = new ErrorLogContext($error, $debugInfo);
        $this->logger->error('', [ErrorLogContext::IDENTIFIER => $context]);

        if ($email !== null) {
            $emailStatus = $this->getEmailStatusFromBounceTypeAndCause($bounceType, $cause);
            $logContext = new TransitionLogContext(toState: $emailStatus->value);
            $email->setStatus($emailStatus);
            $this->logger->info('', context: [TransitionLogContext::IDENTIFIER => $logContext]);
        }

        $this->em->flush();

        if ($email !== null) {
            EmailUuidProcessor::setProcessedMailUuid(null);
        }
    }

    private function logComplaintAndAdjustStatus(
        ComplaintErrorType $complaintType,
        ?string $cause,
        ?string $debugInfo,
        ?Email $email,
    ): void {
        if ($email !== null) {
            EmailUuidProcessor::setProcessedMailUuid($email->getUuid());
        }

        $error = new ComplaintError($complaintType, $cause);
        $context = new ErrorLogContext($error, $debugInfo);
        $this->logger->error('', [ErrorLogContext::IDENTIFIER => $context]);

        if ($email !== null) {
            $emailStatus = EmailStatus::DELIVERY_FAILED_PERMANENTLY;
            $logContext = new TransitionLogContext(toState: $emailStatus->value);
            $email->setStatus($emailStatus);
            $this->logger->info('', context: [TransitionLogContext::IDENTIFIER => $logContext]);
        }

        $this->em->flush();

        if ($email !== null) {
            EmailUuidProcessor::setProcessedMailUuid(null);
        }
    }

    private function getEmailStatusFromBounceTypeAndCause(BounceErrorType $bounceType, ?string $cause): EmailStatus
    {
        if (
            $cause !== null
            && (str_contains(strtolower($cause), 'unable to lookup dns for')
                || str_contains(strtolower($cause), 'invalid domain'))
        ) {
            return EmailStatus::DELIVERY_FAILED_PERMANENTLY;
        }

        return match ($bounceType) {
            BounceErrorType::TRANSIENT => EmailStatus::DELIVERY_FAILED_TEMPORARILY,
            BounceErrorType::PERMANENT => EmailStatus::DELIVERY_FAILED_PERMANENTLY,
            BounceErrorType::UNDETERMINED => EmailStatus::DELIVERY_FAILED_UNDETERMINED,
        };
    }
}
