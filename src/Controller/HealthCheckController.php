<?php

declare(strict_types=1);

namespace App\Controller;

use App\DTO\Response\QueueStatsDto;
use App\Exception\WithStatusCode\UnhealthyInstanceException;
use App\Service\QueueStatsService;
use Doctrine\ORM\EntityManagerInterface;
use Nelmio\ApiDocBundle\Attribute\Model;
use OpenApi\Attributes as OA;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;
use Throwable;

class HealthCheckController extends AbstractController
{
    #[OA\Get(path: '/v1/health', description: 'Check if the service is reachable', summary: '/v1/health',)]
    #[OA\Response(response: '200', description: 'Service available')]
    #[Route(path: '/v1/health', methods: ['GET'])]
    public function health(EntityManagerInterface $entityManager, QueueStatsService $queueStatsService): JsonResponse
    {
        try {
            $entityManager->getConnection()->connect();
            $queueStatsService->getQueueStats();
        } catch (Throwable) {
            throw new UnhealthyInstanceException();
        }

        return new JsonResponse(status: JsonResponse::HTTP_NO_CONTENT);
    }

    /**
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    #[OA\Response(response: '200', description: 'Queue stats', content: new Model(type: QueueStatsDto::class))]
    #[IsGranted("ROLE_ADMIN")]
    #[Route(path: '/v1/health/queue', methods: ['GET'])]
    public function queueStats(QueueStatsService $queueStatsService): JsonResponse
    {
        return new JsonResponse(
            new QueueStatsDto(
                $queueStatsService->getQueueStats()
            )
        );
    }
}
