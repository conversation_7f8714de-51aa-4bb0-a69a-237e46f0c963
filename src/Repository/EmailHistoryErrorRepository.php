<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\EmailHistoryError;
use DateTimeImmutable;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Common\Collections\Criteria;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<EmailHistoryError>
 * @method EmailHistoryError|null find($id, $lockMode = null, $lockVersion = null)
 * @method EmailHistoryError|null findOneBy(array $criteria, array $orderBy = null)
 * @method EmailHistoryError[] findAll()
 * @method EmailHistoryError[] findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class EmailHistoryErrorRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, EmailHistoryError::class);
    }

    public function add(EmailHistoryError $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(EmailHistoryError $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function countFailedSmtpLogins(string $dsn): int
    {
        $dsnLikeOperand = '%' . $dsn . '%';
        $criteria = Criteria::create()
            ->where(Criteria::expr()->orX(
                Criteria::expr()->andX(
                    Criteria::expr()->eq('kind', 'ConnectionError'),
                    Criteria::expr()->eq('type', 'sending'),
                ),
                Criteria::expr()->andX(
                    Criteria::expr()->eq('kind', 'TransportError'),
                    Criteria::expr()->eq('type', 'unknown'),
                )
            ))
            ->andWhere(Criteria::expr()->eq('server', php_uname("n")))
            ->andWhere(Criteria::expr()->gte('created_at', (new DateTimeImmutable())->setTime(0, 0)))
            ->andWhere(Criteria::expr()->lte('created_at', (new DateTimeImmutable())->setTime(23, 59, 59)))
            ->andWhere(Criteria::expr()->contains('dsn', $dsnLikeOperand));

        return $this->matching($criteria)->count();
    }
}
