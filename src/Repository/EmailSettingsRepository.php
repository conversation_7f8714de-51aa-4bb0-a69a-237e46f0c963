<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\EmailSettings;
use App\Enum\Lock;
use App\Security\UserInterface;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<EmailSettings>
 */
class EmailSettingsRepository extends ServiceEntityRepository
{
    use DatabaseLockTrait;

    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, EmailSettings::class);
    }

    public function findByUser(UserInterface $user): ?EmailSettings
    {
        $userId = $user->getUserId();

        return $userId !== null ? $this->find($userId) : null;
    }

    /**
     * @throws ORMException
     * @throws OptimisticLockException
     */
    public function add(EmailSettings $entity, bool $flush = false): void
    {
        $this->_em->persist($entity);

        if ($flush) {
            $this->_em->flush();
        }
    }

    /**
     * @throws ORMException
     * @throws OptimisticLockException
     */
    public function remove(EmailSettings $entity, bool $flush = false): void
    {
        $this->_em->remove($entity);

        if ($flush) {
            $this->_em->flush();
        }
    }

    public function getDeleteLock(): bool
    {
        return $this->getLock(Lock::DELETE);
    }

    public function isDeleteLockFree(): bool
    {
        return $this->isLockFree(Lock::DELETE);
    }
}
