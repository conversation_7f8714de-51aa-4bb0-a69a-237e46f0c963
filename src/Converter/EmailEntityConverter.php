<?php

declare(strict_types=1);

namespace App\Converter;

use App\DTO;
use App\Entity\Email;
use App\Enum\AttachmentContentDisposition;
use App\Error\ValidationError;
use App\Error\ValidationErrorType;
use App\Service\EmailFileService;
use App\Util\Result;
use Exception;
use Symfony\Component\Mime;

class EmailEntityConverter
{
    public function __construct(private readonly EmailFileService $emailFileService)
    {
    }

    public function toResponseDto(Email $email, bool $withBody = false): DTO\Response\EmailSent
    {
        $fromAddress = $email->getFromAddress();
        $actuallySentFromAddress = $email->getActuallySentFromAddress();
        $replyToAddress = $email->getReplyToAddress();

        if ($email->getId() === null) {
            throw new Exception('Email hat keine Id');
        }

        return new DTO\Response\EmailSent(
            id: $email->getId(),
            uuid: (string) $email->getUuid(),
            userId: $email->getUserId(),
            subject: $email->getSubject() ?? '',
            status: $email->getStatus(),
            fromAddress: $fromAddress !== null ? AddressEntityConverter::toResponseDto($fromAddress) : null,
            actuallySentFrom: $actuallySentFromAddress !== null
                ? AddressEntityConverter::toResponseDto($actuallySentFromAddress) : null,
            replyTo: $replyToAddress !== null ? AddressEntityConverter::toResponseDto($replyToAddress) : null,
            toAddress: AddressEntityConverter::toResponseDtos($email->getToAddresses()),
            ccAddress: AddressEntityConverter::toResponseDtos($email->getCcAddresses()),
            bccAddress: AddressEntityConverter::toResponseDtos($email->getBccAddresses()),
            bodyHtml: $withBody
                ? $email->getHtmlPath() !== null
                    ? $this->emailFileService->getContent($email->getHtmlPath())
                    : null
                : null,
            bodyText: $withBody
                ? $email->getTextPath() !== null
                    ? $this->emailFileService->getContent($email->getTextPath())
                    : null
                : null,
            attachments: array_filter(
                array_map(AttachmentEntityConverter::toResponseDto(...), $email->getAttachments()->toArray())
            ),
            sentAt: $email->getSentAt(),
            createdAt: $email->getCreatedAt(),
            errors: array_values(
                array_filter(
                    array_map(
                        EmailHistoryConverter::toErrorResponseDto(...),
                        $email->getHistory()->toArray()
                    )
                )
            ),
        );
    }

    /**
     * @param Email[] $emails
     *
     * @return DTO\Response\EmailSent[]
     */
    public function toResponseDtos(array $emails): array
    {
        return array_map(fn(Email $email) => $this->toResponseDto($email), $emails);
    }

    /**
     * @return Result<Mime\Email, ValidationError[]>
     */
    public function toMimeEmail(Email $emailEntity): Result
    {
        $errors = [];

        if (($validation = self::validateMailEntityProperties($emailEntity))->isErr()) {
            array_push($errors, ...$validation->getError());
        }

        $email = new Mime\Email();
        $email->subject($emailEntity->getSubject() ?? '');

        if ($emailEntity->getFromAddress() !== null) {
            $from = AddressEntityConverter::toMimeAddress($emailEntity->getFromAddress());

            if ($from->isOk()) {
                $email->from($from->getValue());
            } else {
                $errors[] = new ValidationError(ValidationErrorType::FROM_HEADER_INVALID, $from->getError());
            }
        }

        if ($emailEntity->getReplyToAddress() !== null) {
            $replyTo = AddressEntityConverter::toMimeAddress($emailEntity->getReplyToAddress());

            if ($replyTo->isOk()) {
                $email->replyTo($replyTo->getValue());
            } else {
                $errors[] = new ValidationError(ValidationErrorType::REPLY_TO_HEADER_INVALID, $replyTo->getError());
            }
        }

        $to = AddressEntityConverter::toMimeAddresses($emailEntity->getToAddresses());

        if ($to->isOk()) {
            $email->to(...$to->getValue());
        } else {
            array_push(
                $errors,
                ...ValidationError::createFromArray($to->getError(), ValidationErrorType::TO_HEADER_INVALID)
            );
        }

        $cc = AddressEntityConverter::toMimeAddresses($emailEntity->getCcAddresses());

        if ($cc->isOk()) {
            $email->cc(...$cc->getValue());
        } else {
            array_push(
                $errors,
                ...ValidationError::createFromArray($cc->getError(), ValidationErrorType::CC_HEADER_INVALID)
            );
        }

        $bcc = AddressEntityConverter::toMimeAddresses($emailEntity->getBccAddresses());

        if ($bcc->isOk()) {
            $email->bcc(...$bcc->getValue());
        } else {
            array_push(
                $errors,
                ...ValidationError::createFromArray($bcc->getError(), ValidationErrorType::BCC_HEADER_INVALID)
            );
        }

        if ($emailEntity->getTextPath() !== null) {
            $email->text($this->emailFileService->getContent($emailEntity->getTextPath()));
        }

        if ($emailEntity->getHtmlPath() !== null) {
            $email->html($this->emailFileService->getContent($emailEntity->getHtmlPath()));
        }

        if ($emailEntity->getTextPath() === null && $emailEntity->getHtmlPath() === null) {
            $errors[] = new ValidationError(ValidationErrorType::CONTENT_NOT_SET);
        }

        foreach ($emailEntity->getAttachments() as $attachment) {
            $contentType = $attachment->getContentType();

            if ($attachment->getBodyPath() === null) {
                continue;
            }

            if (
                $attachment->getContentDisposition() === AttachmentContentDisposition::INLINE
                && $attachment->getContentId() !== null
            ) {
                $email->addPart(
                    (new Mime\Part\DataPart(
                        body: $this->emailFileService->getContent($attachment->getBodyPath()) ?? '',
                        filename: $attachment->getName(),
                        contentType: $contentType !== '' ? $contentType : null,
                    ))->setContentId($attachment->getContentId())->asInline()
                );
            } else {
                $email->attach(
                    $this->emailFileService->getContent($attachment->getBodyPath()) ?? '',
                    $attachment->getName(),
                    $contentType !== '' ? $contentType : null,
                );
            }
        }

        return $errors === [] ? Result::ok($email) : Result::err($errors);
    }

    /**
     * @return Result<null, ValidationError[]>
     */
    private static function validateMailEntityProperties(Email $email): Result
    {
        $errors = [];

        if (
            count($email->getToAddresses()) === 0
            && count($email->getCcAddresses()) === 0
            && count($email->getBccAddresses()) === 0
        ) {
            $errors[] = new ValidationError(ValidationErrorType::RECIPIENTS_NOT_SET, 'The email has no recipients');
        }

        return $errors === [] ? Result::ok(null) : Result::err($errors);
    }
}
