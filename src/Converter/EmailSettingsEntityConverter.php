<?php

declare(strict_types=1);

namespace App\Converter;

use App\DTO\Response\EmailSettings as EmailSettingsResponseDTO;
use App\DTO\Response\OAuth2AuthConnection;
use App\DTO\Response\SmtpImapConnection;
use App\Entity\EmailSettings as EmailSettingsEntity;
use App\Enum\EmailTransportScheme;

class EmailSettingsEntityConverter
{
    public static function toEmailSettingsResponseDTO(EmailSettingsEntity $settings): EmailSettingsResponseDTO
    {
        return match ($settings->getTransportScheme()) {
            EmailTransportScheme::SMTP,
            EmailTransportScheme::SES_SMTP,
            EmailTransportScheme::SES_SMTPS => self::toSmtpImapConnectionResponseDTO($settings),
            EmailTransportScheme::API => self::toOAuth2ConnectionResponseDTO($settings),
            EmailTransportScheme::SMTPS => throw new \Exception(
                "Conversion of EmailSettings transport scheme 'smtps' not implemented yet."
            ),
        };
    }

    private static function toSmtpImapConnectionResponseDTO(EmailSettingsEntity $settings): EmailSettingsResponseDTO
    {
        if (!$settings instanceof \App\Entity\SmtpImapConnection) {
            throw new \Exception("Cannot convert this EmailSettingsEntity to SmtpImapConnectionResponseDto");
        }

        return new EmailSettingsResponseDTO(
            userId: $settings->getUserId(),
            email: $settings->getEmail(),
            scheme: EmailTransportScheme::SMTP,
            senderName: $settings->getSenderName(),
            sendingStatus: $settings->getSendingStatus(),
            receivingStatus: $settings->getReceivingStatus(),
            smtpImapConnection: new SmtpImapConnection(
                $settings->getSmtpHost(),
                $settings->getSmtpPort(),
                $settings->getUsername(),
                $settings->getImapHost(),
                $settings->getImapPort(),
            )
        );
    }

    private static function toOAuth2ConnectionResponseDTO(EmailSettingsEntity $settings): EmailSettingsResponseDTO
    {
        if (!$settings instanceof \App\Entity\OAuth2Connection) {
            throw new \Exception("Cannot convert this EmailSettingsEntity to OAuth2AuthConnectionResponseDTO");
        }

        return new EmailSettingsResponseDTO(
            userId: $settings->getUserId(),
            email: $settings->getEmail(),
            scheme: EmailTransportScheme::API,
            senderName: $settings->getSenderName(),
            sendingStatus: $settings->getSendingStatus(),
            receivingStatus: $settings->getReceivingStatus(),
            oAuth2AuthConnection: new OAuth2AuthConnection(
                $settings->getProvider(),
                $settings->getExpiresAt(),
                $settings->getData(),
                $settings->getProviderUserId(),
                $settings->getProviderNickname(),
                $settings->getProviderName(),
                $settings->getProviderEmail(),
                $settings->getProviderAvatar(),
            )
        );
    }
}
