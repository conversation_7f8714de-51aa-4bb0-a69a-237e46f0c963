<?php

declare(strict_types=1);

namespace App\Serializer;

use App\DTO\Request\Attachment;
use <PERSON><PERSON><PERSON>ny\Component\PropertyInfo\Type;
use S<PERSON>fony\Component\Serializer\Exception\NotNormalizableValueException;
use Symfony\Component\Serializer\Normalizer\DenormalizerInterface;
use S<PERSON>fony\Component\Serializer\Normalizer\ObjectNormalizer;

class AttachmentDenormalizer implements DenormalizerInterface
{
    public function __construct(private ObjectNormalizer $normalizer)
    {
    }

    public function denormalize(mixed $data, string $type, string $format = null, array $context = []): Attachment
    {
        /** @var Attachment $attachmentDto */
        $attachmentDto = $this->normalizer->denormalize($data, $type, $format, $context);

        $decodedAttachmentBody = base64_decode($attachmentDto->body, true);

        if ($decodedAttachmentBody === false) {
            throw NotNormalizableValueException::createForUnexpectedDataType(
                sprintf(
                    "%s could not be denormalized, as it contains characters outside the base64 charset",
                    $attachmentDto->name
                ),
                $data,
                [Type::BUILTIN_TYPE_STRING],
                is_string($context['deserialization_path']) ? $context['deserialization_path'] : null,
                true
            );
        }

        $attachmentDto->body = $decodedAttachmentBody;

        return $attachmentDto;
    }

    public function supportsDenormalization(mixed $data, string $type, string $format = null): bool
    {
        return $type === Attachment::class;
    }

    /**
     * @return array<true>
     */
    public function getSupportedTypes(?string $format): array
    {
        return [
            Attachment::class => true,
        ];
    }
}
