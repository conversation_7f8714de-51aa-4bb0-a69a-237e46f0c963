<?php

declare(strict_types=1);

namespace App\Entity;

use App\Enum\ConnectionStatus;
use App\Enum\EmailTransportScheme;
use App\Repository\EmailSettingsRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: EmailSettingsRepository::class)]
#[ORM\InheritanceType('JOINED')]
#[ORM\DiscriminatorColumn(name:'transport_scheme', type:'string')]
#[ORM\DiscriminatorMap(['smtp' => SmtpImapConnection::class, 'api' => OAuth2Connection::class])]
#[ORM\HasLifecycleCallbacks]
abstract class EmailSettings
{
    use UpdatedAtCreatedAtTrait;

    public const ERR_MSG_CANT_SEND = 'Those email settings cannot be used for sending';
    public const ERR_MSG_CANT_RECEIVE = 'Those email settings cannot be used for receiving';

    #[ORM\Id]
    #[ORM\Column(name: 'user_id', type: 'integer')]
    protected int $userId;

    protected EmailTransportScheme $transportScheme;

    #[ORM\Column(type: 'string', length: 255)]
    protected string $email;

    #[ORM\Column(name: 'sender_name', type: 'string', length: 255, nullable: true)]
    protected ?string $senderName;

    #[ORM\Column(name: 'sending_status', type: Types::STRING, nullable: true, enumType: ConnectionStatus::class)]
    protected ?ConnectionStatus $sendingStatus;

    #[ORM\Column(name: 'receiving_status', type: Types::STRING, nullable: true, enumType: ConnectionStatus::class)]
    protected ?ConnectionStatus $receivingStatus;

    #[ORM\Column(
        name: 'receiving_failed_connections_attempts',
        type: Types::INTEGER,
        nullable: false,
        options: ['default' => 0]
    )
    ]
    protected int $receivingFailedConnectionsAttempts = 0;

    public function __construct(
        int $userId,
        string $email,
        EmailTransportScheme $transportScheme,
        ?string $senderName = null,
        ?ConnectionStatus $sendingStatus = null,
        ?ConnectionStatus $receivingStatus = null,
    ) {
        $this->userId = $userId;
        $this->email = $email;
        $this->senderName = $senderName;
        $this->transportScheme = $transportScheme;
        $this->sendingStatus = $sendingStatus;
        $this->receivingStatus = $receivingStatus;
    }

    public function getUserId(): int
    {
        return $this->userId;
    }

    public function setUserId(int $userId): self
    {
        $this->userId = $userId;

        return $this;
    }

    public function getEmail(): string
    {
        return $this->email;
    }

    public function setEmail(string $email): self
    {
        $this->email = $email;

        return $this;
    }

    public function getSenderName(): ?string
    {
        return $this->senderName;
    }

    public function setSenderName(?string $senderName): EmailSettings
    {
        $this->senderName = $senderName;

        return $this;
    }

    public function setTransportScheme(EmailTransportScheme $transportScheme): EmailSettings
    {
        $this->transportScheme = $transportScheme;

        return $this;
    }

    public function getTransportScheme(): EmailTransportScheme
    {
        return $this->transportScheme;
    }

    public function getSendingStatus(): ?ConnectionStatus
    {
        return $this->sendingStatus;
    }

    public function setSendingStatus(ConnectionStatus $status): void
    {
        $this->sendingStatus = $status;
    }

    public function getReceivingStatus(): ?ConnectionStatus
    {
        return $this->receivingStatus;
    }

    public function setReceivingStatus(ConnectionStatus $receivingStatus): void
    {
        $this->receivingStatus = $receivingStatus;
    }

    public function setReceivingFailedConnectionsAttempts(int $receivingFailedConnectionsAttempts): EmailSettings
    {
        $this->receivingFailedConnectionsAttempts = $receivingFailedConnectionsAttempts;

        return $this;
    }

    public function getReceivingFailedConnectionsAttempts(): int
    {
        return $this->receivingFailedConnectionsAttempts;
    }

    /**
     * Checks whether all required parameters are present
     */
    public function isIncomplete(): bool
    {
        return $this->email === '';
    }

    abstract public function canSend(): bool;

    abstract public function canReceive(): bool;
}
