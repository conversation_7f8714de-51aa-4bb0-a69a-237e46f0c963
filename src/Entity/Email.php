<?php

declare(strict_types=1);

namespace App\Entity;

use App\Enum\EmailStatus;
use App\EventListener\EmailListener;
use App\Repository\EmailRepository;
use DateTimeImmutable;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Uid\Uuid;

#[ORM\Entity(repositoryClass: EmailRepository::class)]
#[ORM\Index(['status', 'user_id'])]
#[ORM\Index(['sent_at'])]
#[ORM\HasLifecycleCallbacks]
#[ORM\EntityListeners([EmailListener::class])]
class Email
{
    use UpdatedAtCreatedAtTrait;

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private ?int $id;

    // the user id for system users is null
    #[ORM\Column(name: 'user_id', type: 'integer', nullable: true)]
    private ?int $userId;

    #[ORM\Column(type: 'string', length: 32, enumType: EmailStatus::class)]
    private EmailStatus $status;

    #[ORM\Column(name: 'status_callback_url', type: 'text', length: 65535, nullable: true)]
    private ?string $statusCallbackUrl = null;

    #[ORM\Column(name: "from_address", type: 'address', nullable: true)]
    private ?Address $fromAddress;

    #[ORM\Column(name: "actually_sent_from_address", type: 'address', nullable: true)]
    private ?Address $actuallySentFromAddress = null;

    #[ORM\Column(name: "reply_to_address", type: 'address', nullable: true)]
    private ?Address $replyToAddress = null;

    /** @var Address[] */
    #[ORM\Column(name: "to_addresses", type: 'address_array', nullable: true)]
    private array $toAddresses = [];

    /** @var Address[] */
    #[ORM\Column(name: "cc_addresses", type: 'address_array', nullable: true)]
    private array $ccAddresses = [];

    /** @var Address[] */
    #[ORM\Column(name: "bcc_addresses", type: 'address_array', nullable: true)]
    private array $bccAddresses = [];

    #[ORM\Column(type: 'string', length: 1023)]
    private ?string $subject;

    #[ORM\Column(name: 'text_path', type: 'path', length: 255, nullable: true)]
    private ?Path $textPath = null;

    #[ORM\Column(name: 'html_path', type: 'path', length: 255, nullable: true)]
    private ?Path $htmlPath = null;

    /** @var Collection<int, Attachment> */
    #[ORM\OneToMany(mappedBy: 'email', targetEntity: Attachment::class, cascade: ['persist'])]
    private Collection $attachments;

    #[ORM\Column(type: 'datetime_immutable', nullable: true)]
    protected ?DateTimeImmutable $sent_at = null;

    /** @var Collection<int, EmailHistory> */
    #[ORM\OneToMany(mappedBy: 'email', targetEntity: EmailHistory::class)]
    private Collection $history;

    #[ORM\Column(name: 'message_id', type: 'string', length: 1023, nullable: true)]
    private ?string $messageId = null;

    #[ORM\Column(name: 'retry_at', type: 'datetime_immutable', nullable: true)]
    private ?DateTimeImmutable $retryAt = null;

    #[ORM\Column(length: 255)]
    private ?string $serviceId = 'mailer';

    #[ORM\Column]
    private bool $useFailsafeTransport = true;

    #[ORM\Column(type: 'uuid', unique: true)]
    protected Uuid $uuid;

    public function __construct(
        ?int $userId,
        ?string $subject = null,
        ?Address $fromAddress = null,
        EmailStatus $status = EmailStatus::CREATED,
    ) {
        $this->attachments = new ArrayCollection();

        $this->userId = $userId;
        $this->subject = $subject;
        $this->fromAddress = $fromAddress;
        $this->status = $status;
        $this->history = new ArrayCollection();
        $this->uuid = Uuid::v4();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getSubject(): ?string
    {
        return $this->subject;
    }

    public function setSubject(string $subject): self
    {
        $this->subject = $subject;

        return $this;
    }

    public function getTextPath(): ?Path
    {
        return $this->textPath;
    }

    public function setTextPath(Path $textPath): self
    {
        $this->textPath = $textPath;

        return $this;
    }

    public function getUserId(): ?int
    {
        return $this->userId;
    }

    public function setUserId(int $userId): Email
    {
        $this->userId = $userId;

        return $this;
    }

    public function setHtmlPath(?Path $htmlPath): Email
    {
        $this->htmlPath = $htmlPath;

        return $this;
    }

    public function getHtmlPath(): ?Path
    {
        return $this->htmlPath;
    }

    public function setFromAddress(Address $fromAddress): Email
    {
        $this->fromAddress = $fromAddress;

        return $this;
    }

    public function getFromAddress(): ?Address
    {
        return $this->fromAddress;
    }

    public function setReplyToAddress(Address $replyToAddress): Email
    {
        $this->replyToAddress = $replyToAddress;

        return $this;
    }

    public function getReplyToAddress(): ?Address
    {
        return $this->replyToAddress;
    }

    /**
     * @param Address[] $toAddresses
     */
    public function setToAddresses(array $toAddresses): Email
    {
        $this->toAddresses = $toAddresses;

        return $this;
    }

    /**
     * @return Address[]
     */
    public function getToAddresses(): array
    {
        return $this->toAddresses;
    }

    /**
     * @param Address[] $ccAddresses
     */
    public function setCcAddresses(array $ccAddresses): Email
    {
        $this->ccAddresses = $ccAddresses;

        return $this;
    }

    /**
     * @return Address[]
     */
    public function getCcAddresses(): array
    {
        return $this->ccAddresses;
    }

    /**
     * @param Address[] $bccAddresses
     */
    public function setBccAddresses(array $bccAddresses): Email
    {
        $this->bccAddresses = $bccAddresses;

        return $this;
    }

    /**
     * @return Address[]
     */
    public function getBccAddresses(): array
    {
        return $this->bccAddresses;
    }

    public function setStatus(EmailStatus $status): Email
    {
        $this->status = $status;

        return $this;
    }

    public function getStatus(): EmailStatus
    {
        return $this->status;
    }

    public function addAttachment(Attachment $attachment): self
    {
        $this->attachments->add($attachment);
        $attachment->setEmail($this);

        return $this;
    }

    /**
     * @return Collection<int, Attachment>
     */
    public function getAttachments(): Collection
    {
        return $this->attachments;
    }

    public function setActuallySentFromAddress(?Address $actuallySentFromAddress): self
    {
        $this->actuallySentFromAddress = $actuallySentFromAddress;

        return $this;
    }

    public function getActuallySentFromAddress(): ?Address
    {
        return $this->actuallySentFromAddress;
    }

    public function setSentAt(DateTimeImmutable $sent_at): self
    {
        $this->sent_at = $sent_at;

        return $this;
    }

    public function getSentAt(): ?DateTimeImmutable
    {
        return $this->sent_at;
    }

    /**
     * @return Collection<int, EmailHistory>
     */
    public function getHistory(): Collection
    {
        return $this->history;
    }

    public function addHistory(EmailHistory $history): self
    {
        if (!$this->history->contains($history)) {
            $this->history->add($history);
            $history->setEmail($this);
        }

        return $this;
    }

    public function removeHistory(EmailHistory $history): self
    {
        if ($this->history->removeElement($history)) {
            // set the owning side to null (unless already changed)
            if ($history->getEmail() === $this) {
                $history->setEmail(null);
            }
        }

        return $this;
    }

    public function setMessageId(?string $messageId): Email
    {
        $this->messageId = $messageId;

        return $this;
    }

    public function getMessageId(): ?string
    {
        return $this->messageId;
    }

    public function getStatusCallbackUrl(): ?string
    {
        return $this->statusCallbackUrl;
    }

    public function setStatusCallbackUrl(?string $statusCallbackUrl): Email
    {
        $this->statusCallbackUrl = $statusCallbackUrl;

        return $this;
    }

    /**
     * @return DateTimeImmutable|null
     */
    public function getRetryAt(): ?DateTimeImmutable
    {
        return $this->retryAt;
    }

    /**
     * @param DateTimeImmutable|null $retryAt
     */
    public function setRetryAt(?DateTimeImmutable $retryAt): void
    {
        $this->retryAt = $retryAt;
    }

    public function getServiceId(): ?string
    {
        return $this->serviceId;
    }

    public function setServiceId(string $serviceId): static
    {
        $this->serviceId = $serviceId;

        return $this;
    }

    public function getUseFailsafeTransport(): bool
    {
        return $this->useFailsafeTransport;
    }

    public function setUseFailsafeTransport(bool $useFailsafeTransport): static
    {
        $this->useFailsafeTransport = $useFailsafeTransport;

        return $this;
    }

    public function getUuid(): Uuid
    {
        return $this->uuid;
    }

    public function setUuid(Uuid $uuid): static
    {
        $this->uuid = $uuid;

        return $this;
    }
}
