{"license": "proprietary", "type": "project", "require": {"php": "^8.2", "ext-ctype": "*", "ext-iconv": "*", "ext-imap": "*", "ext-openssl": "*", "ext-redis": "*", "ext-zip": "^1.19", "aws/aws-sdk-php-symfony": "^2.5", "demv/professionalworks-sdk": "dev-main", "demvsystems/tmpdir": "^1.0", "doctrine/annotations": "^1.0", "doctrine/doctrine-bundle": "^2.6", "doctrine/doctrine-migrations-bundle": "^3.2", "doctrine/orm": "^2.12", "doctrineencryptbundle/doctrine-encrypt-bundle": "^5.3", "google/apiclient": "^2.12", "jms/serializer-bundle": "^5.5", "league/flysystem-aws-s3-v3": "^3.0", "league/flysystem-bundle": "^2.3", "league/html-to-markdown": "^5.1", "lexik/jwt-authentication-bundle": "^2.16", "microsoft/microsoft-graph": "^1.108", "nelmio/api-doc-bundle": "^5.0", "phpdocumentor/reflection-docblock": "^5.3", "phpstan/phpdoc-parser": "^1.5", "sentry/sentry-symfony": "^5.0", "symfony/amazon-mailer": "^6.4", "symfony/asset": "~6.4.0", "symfony/console": "^6.4", "symfony/dotenv": "^6.4", "symfony/expression-language": "^6.4", "symfony/flex": "^2", "symfony/framework-bundle": "^6.4", "symfony/mailer": "^6.4", "symfony/messenger": "~6.4.0", "symfony/monolog-bundle": "^3.8", "symfony/polyfill-uuid": "^1.26", "symfony/property-access": "^6.4", "symfony/property-info": "^6.4", "symfony/proxy-manager-bridge": "^6.4", "symfony/redis-messenger": "~6.4.0", "symfony/runtime": "^6.4", "symfony/security-bundle": "^6.4", "symfony/serializer": "^6.4", "symfony/twig-bundle": "~6.4.0", "symfony/uid": "~6.4.0", "symfony/validator": "^6.4", "symfony/yaml": "^6.4", "thenetworg/oauth2-azure": "^2.2", "webklex/php-imap": "^5.5"}, "require-dev": {"brianium/paratest": "^7.1", "doctrine/doctrine-fixtures-bundle": "^3.4", "ergebnis/composer-normalize": "^2.30", "league/flysystem-memory": "^3.0", "liip/test-fixtures-bundle": "^2.4", "phpstan/phpstan": "^1.10", "phpstan/phpstan-deprecation-rules": "^1.1", "phpstan/phpstan-doctrine": "^1.3", "phpstan/phpstan-phpunit": "^1.3", "phpstan/phpstan-strict-rules": "^1.5", "phpstan/phpstan-symfony": "^1.2", "phpunit/phpunit": "^10.0", "slevomat/coding-standard": "^8.1", "spaceemotion/php-coding-standard": "dev-wip/v1", "squizlabs/php_codesniffer": "^3.9", "symfony/browser-kit": "^6.4", "symfony/maker-bundle": "^1.41", "symfony/phpunit-bridge": "^6.4", "symfony/stopwatch": "~6.4.0", "symfony/var-dumper": "^6.4", "symfony/web-profiler-bundle": "~6.4.0"}, "replace": {"symfony/polyfill-ctype": "*", "symfony/polyfill-iconv": "*", "symfony/polyfill-php72": "*", "symfony/polyfill-php73": "*", "symfony/polyfill-php74": "*", "symfony/polyfill-php80": "*"}, "conflict": {"symfony/symfony": "*"}, "repositories": [{"type": "git", "url": "**************:demvsystems/professionalworks-sdk.git", "no-api": true}, {"type": "git", "url": "**************:demvsystems/sdk-framework.git", "no-api": true}, {"type": "git", "url": "**************:demvsystems/tmpdir.git", "no-api": true}], "minimum-stability": "dev", "prefer-stable": true, "autoload": {"psr-4": {"App\\": "src/"}}, "autoload-dev": {"psr-4": {"App\\Tests\\": "tests/", "App\\Tests\\Integration\\": "tests/integration/", "App\\Tests\\Unit\\": "tests/unit/", "App\\Tests\\Util\\": "tests/util/", "App\\Tests\\Util\\Factory\\": "tests/util/factory/"}}, "config": {"allow-plugins": {"composer/package-versions-deprecated": true, "dealerdirect/phpcodesniffer-composer-installer": false, "ergebnis/composer-normalize": true, "php-http/discovery": false, "symfony/flex": true, "symfony/runtime": true}, "optimize-autoloader": true, "preferred-install": {"*": "dist"}, "sort-packages": true}, "extra": {"google/apiclient-services": ["Gmail"], "symfony": {"allow-contrib": true, "require": "6.4.*"}}, "scripts": {"post-install-cmd": ["@auto-scripts"], "post-update-cmd": ["@auto-scripts"], "pre-autoload-dump": "Google\\Task\\Composer::cleanup", "auto-scripts": {"cache:clear": "symfony-cmd"}}}