jms_serializer:
    visitors:
        xml_serialization:
            format_output: '%kernel.debug%'
    property_naming:
        id: 'jms_serializer.identical_property_naming_strategy'
    object_constructors:
        doctrine:
            fallback_strategy: "fallback"
            
when@prod:
    jms_serializer:
        visitors:
            json_serialization:
                options:
                    - JSO<PERSON>_UNESCAPED_SLASHES
                    - JSON_PRESERVE_ZERO_FRACTION

when@dev:
    jms_serializer:
        visitors:
            json_serialization:
                options:
                    - JSON_PRETTY_PRINT
                    - JSON_UNESCAPED_SLASHES
                    - JSON_PRESERVE_ZERO_FRACTION
