when@dev:
    app.swagger_ui:
        path: /doc/openapi
        methods: GET
        defaults: { _controller: nelmio_api_doc.controller.swagger_ui }

    app.redocly:
        path: /doc/redocly
        methods: GET
        defaults: { _controller: nelmio_api_doc.controller.redocly }

    app.swagger:
        path: /doc/doc.json
        methods: GET
        defaults: { _controller: nelmio_api_doc.controller.swagger }
