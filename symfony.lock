{"aws/aws-crt-php": {"version": "v1.0.2"}, "aws/aws-sdk-php": {"version": "3.225.5"}, "aws/aws-sdk-php-symfony": {"version": "2.5", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "1.3", "ref": "d1753f9e2a669c464b2b0618af9b0123426b67b4"}, "files": ["config/packages/aws.yaml"]}, "brianium/paratest": {"version": "v6.4.4"}, "composer/pcre": {"version": "1.0.1"}, "composer/xdebug-handler": {"version": "2.0.5"}, "dealerdirect/phpcodesniffer-composer-installer": {"version": "v0.7.2"}, "demv/professionalworks-sdk": {"version": "dev-main"}, "demv/sdk-framework": {"version": "dev-main"}, "doctrine/annotations": {"version": "1.13", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "1.10", "ref": "64d8583af5ea57b7afa4aba4b159907f3a148b05"}}, "doctrine/cache": {"version": "2.1.1"}, "doctrine/collections": {"version": "1.6.8"}, "doctrine/common": {"version": "3.3.0"}, "doctrine/data-fixtures": {"version": "1.5.3"}, "doctrine/dbal": {"version": "3.3.6"}, "doctrine/deprecations": {"version": "v0.5.3"}, "doctrine/doctrine-bundle": {"version": "2.6", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "2.4", "ref": "ddddd8249dd55bbda16fa7a45bb7499ef6f8e90e"}, "files": ["config/packages/doctrine.yaml", "src/Entity/.gitignore", "src/Repository/.gitignore"]}, "doctrine/doctrine-fixtures-bundle": {"version": "3.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "3.0", "ref": "1f5514cfa15b947298df4d771e694e578d4c204d"}, "files": ["src/DataFixtures/AppFixtures.php"]}, "doctrine/doctrine-migrations-bundle": {"version": "3.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "3.1", "ref": "ee609429c9ee23e22d6fa5728211768f51ed2818"}, "files": ["config/packages/doctrine_migrations.yaml", "migrations/.gitignore"]}, "doctrine/event-manager": {"version": "1.1.1"}, "doctrine/inflector": {"version": "2.0.4"}, "doctrine/instantiator": {"version": "1.4.1"}, "doctrine/lexer": {"version": "1.2.3"}, "doctrine/migrations": {"version": "3.5.0"}, "doctrine/orm": {"version": "2.12.2"}, "doctrine/persistence": {"version": "3.0.2"}, "doctrine/sql-formatter": {"version": "1.1.2"}, "doctrineencryptbundle/doctrine-encrypt-bundle": {"version": "5.3.1"}, "egulias/email-validator": {"version": "3.1.2"}, "ergebnis/composer-normalize": {"version": "2.25.2"}, "ergebnis/json-normalizer": {"version": "2.1.0"}, "ergebnis/json-printer": {"version": "3.2.0"}, "ergebnis/json-schema-validator": {"version": "2.0.0"}, "firebase/php-jwt": {"version": "v6.1.2"}, "friendsofphp/proxy-manager-lts": {"version": "v1.0.12"}, "google/apiclient": {"version": "2.12", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "2.0", "ref": "49c5d7a66d863d28513979bf0666dc77599100c0"}, "files": ["config/packages/google_apiclient.yaml"]}, "google/apiclient-services": {"version": "v0.251.0"}, "google/auth": {"version": "v1.21.0"}, "guzzlehttp/guzzle": {"version": "7.4.2"}, "guzzlehttp/promises": {"version": "1.5.1"}, "guzzlehttp/psr7": {"version": "2.2.1"}, "illuminate/collections": {"version": "v9.11.0"}, "illuminate/conditionable": {"version": "v9.11.0"}, "illuminate/contracts": {"version": "v9.11.0"}, "illuminate/macroable": {"version": "v9.11.0"}, "jean85/pretty-package-versions": {"version": "2.0.5"}, "jms/serializer-bundle": {"version": "5.5", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "4.0", "ref": "cc04e10cf7171525b50c18b36004edf64cb478be"}, "files": ["config/packages/jms_serializer.yaml"]}, "justinrainbow/json-schema": {"version": "5.2.12"}, "laminas/laminas-code": {"version": "4.5.1"}, "league/flysystem": {"version": "3.0.21"}, "league/flysystem-aws-s3-v3": {"version": "3.0.21"}, "league/flysystem-bundle": {"version": "2.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "1.0", "ref": "913dc3d7a5a1af0d2b044c5ac3a16e2f851d7380"}, "files": ["config/packages/flysystem.yaml", "var/storage/.gitignore"]}, "league/flysystem-memory": {"version": "3.0.14"}, "league/mime-type-detection": {"version": "1.11.0"}, "lexik/jwt-authentication-bundle": {"version": "2.16", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "2.5", "ref": "5b2157bcd5778166a5696e42f552ad36529a07a6"}, "files": ["config/packages/lexik_jwt_authentication.yaml"]}, "liip/test-fixtures-bundle": {"version": "2.4.0"}, "localheinz/diff": {"version": "1.1.1"}, "masterminds/html5": {"version": "2.7.5"}, "monolog/monolog": {"version": "2.6.0"}, "mtdowling/jmespath.php": {"version": "2.6.1"}, "myclabs/deep-copy": {"version": "1.11.0"}, "nelmio/api-doc-bundle": {"version": "4.34", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "3.0", "ref": "c8e0c38e1a280ab9e37587a8fa32b251d5bc1c94"}, "files": ["config/packages/nelmio_api_doc.yaml", "config/routes/nelmio_api_doc.yaml"]}, "nesbot/carbon": {"version": "2.58.0"}, "nikic/php-parser": {"version": "v4.13.2"}, "paragonie/constant_time_encoding": {"version": "v2.5.0"}, "paragonie/random_compat": {"version": "v9.99.100"}, "phar-io/manifest": {"version": "2.0.3"}, "phar-io/version": {"version": "3.2.1"}, "phpdocumentor/reflection-common": {"version": "2.2.0"}, "phpdocumentor/reflection-docblock": {"version": "5.3.0"}, "phpdocumentor/type-resolver": {"version": "1.6.1"}, "phpseclib/phpseclib": {"version": "3.0.14"}, "phpstan/phpdoc-parser": {"version": "1.4.4"}, "phpstan/phpstan": {"version": "1.5.7"}, "phpstan/phpstan-deprecation-rules": {"version": "1.0.0"}, "phpstan/phpstan-doctrine": {"version": "1.3.3"}, "phpstan/phpstan-phpunit": {"version": "1.1.1"}, "phpstan/phpstan-strict-rules": {"version": "1.1.0"}, "phpstan/phpstan-symfony": {"version": "1.1.8"}, "phpunit/php-code-coverage": {"version": "9.2.15"}, "phpunit/php-file-iterator": {"version": "3.0.6"}, "phpunit/php-invoker": {"version": "3.1.1"}, "phpunit/php-text-template": {"version": "2.0.4"}, "phpunit/php-timer": {"version": "5.0.3"}, "phpunit/phpunit": {"version": "9.5", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "9.3", "ref": "a6249a6c4392e9169b87abf93225f7f9f59025e6"}, "files": [".env.test", "phpunit.xml.dist", "tests/bootstrap.php"]}, "psr/cache": {"version": "3.0.0"}, "psr/container": {"version": "2.0.2"}, "psr/event-dispatcher": {"version": "1.0.0"}, "psr/http-client": {"version": "1.0.1"}, "psr/http-factory": {"version": "1.0.1"}, "psr/http-message": {"version": "1.0.1"}, "psr/log": {"version": "3.0.0"}, "psr/simple-cache": {"version": "3.0.0"}, "ralouphie/getallheaders": {"version": "3.0.3"}, "sebastian/cli-parser": {"version": "1.0.1"}, "sebastian/code-unit": {"version": "1.0.8"}, "sebastian/code-unit-reverse-lookup": {"version": "2.0.3"}, "sebastian/comparator": {"version": "4.0.6"}, "sebastian/complexity": {"version": "2.0.2"}, "sebastian/diff": {"version": "4.0.4"}, "sebastian/environment": {"version": "5.1.4"}, "sebastian/exporter": {"version": "4.0.4"}, "sebastian/global-state": {"version": "5.0.5"}, "sebastian/lines-of-code": {"version": "1.0.3"}, "sebastian/object-enumerator": {"version": "4.0.4"}, "sebastian/object-reflector": {"version": "2.0.4"}, "sebastian/recursion-context": {"version": "4.0.4"}, "sebastian/type": {"version": "3.0.0"}, "sebastian/version": {"version": "3.0.2"}, "sentry/sentry": {"version": "3.5.0"}, "sentry/sentry-symfony": {"version": "4.2", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "3.0", "ref": "9746f0823302d7980e5273ef7a69ef3f5ac80914"}, "files": ["config/packages/sentry.yaml"]}, "slevomat/coding-standard": {"version": "7.1"}, "spaceemotion/php-coding-standard": {"version": "dev-wip/v1"}, "squizlabs/php_codesniffer": {"version": "3.6", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "3.6", "ref": "1019e5c08d4821cb9b77f4891f8e9c31ff20ac6f"}}, "symfony/amazon-mailer": {"version": "6.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "4.4", "ref": "9648db3ecae5c8a6b1a5f74715d3907124348815"}}, "symfony/browser-kit": {"version": "v6.0.3"}, "symfony/cache": {"version": "v6.0.6"}, "symfony/cache-contracts": {"version": "v3.0.1"}, "symfony/config": {"version": "v6.0.7"}, "symfony/console": {"version": "6.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "5.3", "ref": "da0c8be8157600ad34f10ff0c9cc91232522e047"}, "files": ["bin/console"]}, "symfony/dependency-injection": {"version": "v6.0.7"}, "symfony/deprecation-contracts": {"version": "v3.0.1"}, "symfony/doctrine-bridge": {"version": "v6.0.8"}, "symfony/dom-crawler": {"version": "v6.0.6"}, "symfony/dotenv": {"version": "v6.0.5"}, "symfony/error-handler": {"version": "v6.0.7"}, "symfony/event-dispatcher": {"version": "v6.0.3"}, "symfony/event-dispatcher-contracts": {"version": "v3.0.1"}, "symfony/filesystem": {"version": "v6.0.7"}, "symfony/finder": {"version": "v6.0.3"}, "symfony/flex": {"version": "2.1", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "1.0", "ref": "c0eeb50665f0f77226616b6038a9b06c03752d8e"}, "files": [".env"]}, "symfony/framework-bundle": {"version": "6.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "5.4", "ref": "3cd216a4d007b78d8554d44a5b1c0a446dab24fb"}, "files": ["config/packages/cache.yaml", "config/packages/framework.yaml", "config/preload.php", "config/routes/framework.yaml", "config/services.yaml", "public/index.php", "src/Controller/.gitignore", "src/Kernel.php"]}, "symfony/http-client": {"version": "v6.0.9"}, "symfony/http-client-contracts": {"version": "v3.1.0"}, "symfony/http-foundation": {"version": "v6.0.7"}, "symfony/http-kernel": {"version": "v6.0.7"}, "symfony/mailer": {"version": "6.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "4.3", "ref": "97a61eabb351d7f6cb7702039bcfe07fe9d7e03c"}, "files": ["config/packages/mailer.yaml"]}, "symfony/maker-bundle": {"version": "1.41", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "1.0", "ref": "fadbfe33303a76e25cb63401050439aa9b1a9c7f"}}, "symfony/messenger": {"version": "6.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.0", "ref": "ba1ac4e919baba5644d31b57a3284d6ba12d52ee"}, "files": ["config/packages/messenger.yaml"]}, "symfony/mime": {"version": "v6.0.8"}, "symfony/monolog-bridge": {"version": "v6.0.3"}, "symfony/monolog-bundle": {"version": "3.8", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "3.7", "ref": "213676c4ec929f046dfde5ea8e97625b81bc0578"}, "files": ["config/packages/monolog.yaml"]}, "symfony/options-resolver": {"version": "v6.0.3"}, "symfony/password-hasher": {"version": "v6.0.8"}, "symfony/phpunit-bridge": {"version": "6.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "5.3", "ref": "97cb3dc7b0f39c7cfc4b7553504c9d7b7795de96"}, "files": [".env.test", "bin/phpunit", "phpunit.xml.dist", "tests/bootstrap.php"]}, "symfony/polyfill-intl-grapheme": {"version": "v1.25.0"}, "symfony/polyfill-intl-idn": {"version": "v1.25.0"}, "symfony/polyfill-intl-normalizer": {"version": "v1.25.0"}, "symfony/polyfill-mbstring": {"version": "v1.25.0"}, "symfony/polyfill-uuid": {"version": "v1.25.0"}, "symfony/process": {"version": "v6.0.7"}, "symfony/property-access": {"version": "v6.0.8"}, "symfony/property-info": {"version": "v6.0.7"}, "symfony/proxy-manager-bridge": {"version": "v6.0.6"}, "symfony/psr-http-message-bridge": {"version": "v2.1.2"}, "symfony/routing": {"version": "6.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "6.0", "ref": "eb3b377a4dc07006c4bdb2c773652cc9434f5246"}, "files": ["config/packages/routing.yaml", "config/routes.yaml"]}, "symfony/runtime": {"version": "v6.0.7"}, "symfony/security-bundle": {"version": "6.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "5.3", "ref": "98f1f2b0d635908c2b40f3675da2d23b1a069d30"}, "files": ["config/packages/security.yaml"]}, "symfony/security-core": {"version": "v6.0.8"}, "symfony/security-csrf": {"version": "v6.0.3"}, "symfony/security-http": {"version": "v6.0.8"}, "symfony/serializer": {"version": "v6.0.8"}, "symfony/service-contracts": {"version": "v3.0.1"}, "symfony/stopwatch": {"version": "v6.0.5"}, "symfony/string": {"version": "v6.0.3"}, "symfony/translation": {"version": "6.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "5.3", "ref": "da64f5a2b6d96f5dc24914517c0350a5f91dee43"}, "files": ["config/packages/translation.yaml", "translations/.gitignore"]}, "symfony/translation-contracts": {"version": "v3.0.1"}, "symfony/twig-bundle": {"version": "6.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.4", "ref": "cab5fd2a13a45c266d45a7d9337e28dee6272877"}, "files": ["config/packages/twig.yaml", "templates/base.html.twig"]}, "symfony/uid": {"version": "6.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.2", "ref": "d294ad4add3e15d7eb1bae0221588ca89b38e558"}, "files": ["config/packages/uid.yaml"]}, "symfony/validator": {"version": "6.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "5.3", "ref": "c32cfd98f714894c4f128bb99aa2530c1227603c"}, "files": ["config/packages/validator.yaml"]}, "symfony/var-dumper": {"version": "v6.0.6"}, "symfony/var-exporter": {"version": "v6.0.7"}, "symfony/web-profiler-bundle": {"version": "6.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.1", "ref": "8b51135b84f4266e3b4c8a6dc23c9d1e32e543b7"}, "files": ["config/packages/web_profiler.yaml", "config/routes/web_profiler.yaml"]}, "symfony/yaml": {"version": "v6.0.3"}, "theseer/tokenizer": {"version": "1.2.1"}, "webmozart/assert": {"version": "1.10.0"}, "zircote/swagger-php": {"version": "4.4.1"}}