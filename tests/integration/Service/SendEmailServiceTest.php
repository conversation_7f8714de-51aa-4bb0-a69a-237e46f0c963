<?php

declare(strict_types=1);

namespace App\Tests\Integration\Service;

use App\Converter\EmailDtoConverter;
use App\DataFixtures\EmailSettingsFixtures;
use App\DTO\UserInfo;
use App\Entity\Address;
use App\Entity\Email;
use App\Entity\EmailHistoryError;
use App\Entity\EmailHistoryTransition;
use App\Entity\EmailSettings;
use App\Entity\Path;
use App\Entity\SmtpImapConnection;
use App\Enum\EmailStatus;
use App\Enum\ProviderConfigValidationError;
use App\Error\TransportError;
use App\Error\TransportErrorType;
use App\EventListener\EmailListener;
use App\Service\EmailFileService;
use App\Service\EmailTransportService;
use App\Service\SendEmailService;
use App\Service\UserInfoService;
use App\Service\UserInfoServiceInterface;
use App\Tests\Util\Factory\UserFactory;
use Doctrine\ORM\EntityManagerInterface;
use Liip\TestFixturesBundle\Services\DatabaseToolCollection;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\Attributes\Test;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;

class SendEmailServiceTest extends WebTestCase
{
    private EntityManagerInterface $em;

    public function setUp(): void
    {
        static::tearDown();
        $client = static::createClient();
        $client->loginUser(UserFactory::createPwUser());
        $container = self::getContainer();

        $emailListener = $this->createMock(EmailListener::class);
        $container->set(EmailListener::class, $emailListener);

        // prepare database
        $container
            ->get(DatabaseToolCollection::class)
            ->get()
            ->loadFixtures([EmailSettingsFixtures::class]);

        $this->em = self::getContainer()->get(EntityManagerInterface::class);

        EmailTransportService::emptyTransportCache();
    }

    #[Test]
    #[DataProvider('getRateLimitTestData')]
    public function it_does_not_send_when_rate_limit_is_reached(int $iterations, EmailStatus $expectedStatus): void
    {
        // Todo: MAILER-573
        self::markTestSkipped(
            'This test should mock the RateLimitService and only test, that the mail is going to be requeued. ' .
            'This test should be adjusted accordingly in MAILER-573'
        );

        $this->clearEmailsFromTestDb();
        $emailRepository = $this->em->getRepository(Email::class);
        $userEmailSettingsRepository = $this->em->getRepository(EmailSettings::class);

        /** @var SmtpImapConnection $userSettings */
        $userSettings = $userEmailSettingsRepository->find(1);
        $userSettings->setEmail('<EMAIL>');
        $userSettings->setImapHost('df.eu');
        $userSettings->setSmtpHost('df.eu');

        $this->em->flush();

        for ($i = 0; $i < $iterations; $i++) {
            $this->addEmailToQueue($this->createEmail(EmailStatus::SENT, $userSettings->getEmail()));
        }

        $email = $this->createEmail(EmailStatus::QUEUED, $userSettings->getEmail());
        $this->addEmailToQueue($email);

        self::getContainer()->get(SendEmailService::class)->sendQueuedEmails(1);

        /** @var Email $result */
        $result = $emailRepository->find($email->getId());

        self::assertEquals($expectedStatus, $result->getStatus());
    }

    #[Test]
    #[DataProvider('getMaxConnectionErrorTestData')]
    public function it_tests_if_rate_limit_is_reached(int $iterations, int $expectedErrorCount): void
    {
        // Todo: MAILER-573
        self::markTestSkipped('This test should mock the amount of emails in the db without actual db calls');

        $this->clearEmailsFromTestDb();
        $userEmailSettingsRepository = $this->em->getRepository(EmailSettings::class);

        /** @var SmtpImapConnection $userSettings */
        $userSettings = $userEmailSettingsRepository->find(1);
        $userSettings->setEmail('<EMAIL>');
        $userSettings->setImapHost('strato.de');
        $userSettings->setSmtpHost('strato.de');

        $transportError = new TransportError(TransportErrorType::UNKNOWN, 'test');

        for ($i = 0; $i < $iterations; $i++) {
            $emailHistoryError = new EmailHistoryError(
                error: $transportError,
                server: php_uname("n"),
                dsn: 'strato.de'
            );
            $emailHistoryError->setKind('TransportError');
            $this->em->persist($emailHistoryError);
            $this->em->flush();
        }

        $email = $this->createEmail(EmailStatus::QUEUED);
        $this->addEmailToQueue($email);
        self::getContainer()->get(SendEmailService::class)->sendQueuedEmails(1);

        $countMaxConnectionError = $this->em->getRepository(EmailHistoryError::class)->count(
            [
                'cause' => ProviderConfigValidationError::MAX_CONNECTION_ERRORS_REACHED,
            ]
        );

        self::assertEquals($expectedErrorCount, $countMaxConnectionError);
    }

    /**
     * @test
     */
    public function it_starts_the_statemachine_with_state_queued_when_sending_from_queue(): void
    {
        $this->clearEmailsFromTestDb();
        $emailRepository = $this->em->getRepository(Email::class);

        $email = $this->createEmail(EmailStatus::QUEUED);
        $this->addEmailToQueue($email);

        self::getContainer()->get(SendEmailService::class)->handle($email);
        /** @var Email $firstEmailResult */
        $firstEmailResult = $emailRepository->find($email->getId());
        /** @var EmailHistoryTransition $firstHistoryItem */
        $firstHistoryItem = $firstEmailResult->getHistory()[0];

        self::assertEquals(EmailStatus::QUEUED->value, $firstHistoryItem->getFromState());
        self::assertEquals('Posted', $firstHistoryItem->getToState());
    }

    /**
     * @test
     */
    public function it_sets_reply_to_correctly_for_users_without_email_settings(): void
    {
        $this->clearEmailsFromTestDb();

        $emailFileServiceMock = $this->createMock(EmailFileService::class);
        $emailFileServiceMock
            ->expects(self::once())
            ->method('getContent')
            ->willReturn('test');
        self::getContainer()->set(EmailFileService::class, $emailFileServiceMock);

        $userInfoServiceMock = $this->createMock(UserInfoService::class);
        $userInfoServiceMock
            ->expects(self::exactly(2))
            ->method('getUserInfo')
            ->willReturn(new UserInfo(42, [], '<EMAIL>'));
        self::getContainer()->set(UserInfoServiceInterface::class, $userInfoServiceMock);

        $emailRepository = $this->em->getRepository(Email::class);

        $email = new Email(userId: 42, subject: 'test', status: EmailStatus::QUEUED);
        $email->setTextPath(new Path('test'));
        $email->setToAddresses([new Address('<EMAIL>')]);
        $this->addEmailToQueue($email, true);

        self::getContainer()->get(SendEmailService::class)->handle($email);
        /** @var Email $emailResult */
        $emailResult = $emailRepository->find($email->getId());

        // The Email is sent by the fallback transport and the from address accordingly
        self::assertEquals('<EMAIL>', $emailResult->getActuallySentFromAddress()?->address);
        // The Email address of the user is used as reply to address
        self::assertEquals('<EMAIL>', $emailResult->getReplyToAddress()?->address);
    }

    /**
     * @return array<array{float[], int}> Sending delay + expected number of successes
     */
    public static function getTimeoutData(): array
    {
        $timeout = self::getContainer()->getParameter('app.batch_timeout');

        return [
            [[$timeout + 0.01], 1],
            [[0, $timeout + 0.01], 2],
            [[0, 0, $timeout + 0.01], 3],
            [[], 9],
        ];
    }

    /**
     * @return array<array{iterations: int, expectedStatus: EmailStatus}>
     */
    public static function getRateLimitTestData(): array
    {
        return [
            ['iterations' => 451, 'expectedStatus' => EmailStatus::QUEUED],
            ['iterations' => 449, 'expectedStatus' => EmailStatus::SENT],
        ];
    }

    /**
     * @return array<array{iterations: int, expectedErrorCount: int}>
     */
    public static function getMaxConnectionErrorTestData(): array
    {
        return [
            ['iterations' => 30, 'expectedErrorCount' => 1],
            ['iterations' => 10, 'expectedErrorCount' => 0],
        ];
    }

    private function addEmailToQueue(Email $emailEntity, bool $useFailSafeTransport = false): void
    {
        $emailEntity->setUseFailsafeTransport($useFailSafeTransport);
        $this->em->persist($emailEntity);
        $this->em->flush();
    }

    private function clearEmailsFromTestDb(): void
    {
        //remove old entities
        $emailRepository = $this->em->getRepository(Email::class);
        $emails = $emailRepository->findAll();

        foreach ($emails as $email) {
            $this->em->remove($email);
        }
    }

    public function mockUserInfoService(int $expectedCalls = 1): void
    {
        $userInfoServiceMock = $this->createMock(UserInfoService::class);
        $userInfoServiceMock
            ->expects(self::exactly($expectedCalls))
            ->method('getUserInfo')
            ->willReturn(new UserInfo(42, [], '<EMAIL>'));
        self::getContainer()->set(UserInfoServiceInterface::class, $userInfoServiceMock);
    }

    private function createEmail(EmailStatus $emailStatus, string $actuallySentFrom = '<EMAIL>'): Email
    {
        $emailDto = new \App\DTO\Request\Email("test");
        $emailDto->text = 'Test';
        $emailDto->to = [new \App\DTO\Request\Address('<EMAIL>')];

        /** @var EmailDtoConverter $emailDtoConverter */
        $emailDtoConverter = self::getContainer()->get(EmailDtoConverter::class);
        $email = $emailDtoConverter->toEmailEntity($emailDto, UserFactory::createPwUser());
        $email->setStatus($emailStatus);

        if ($emailStatus === EmailStatus::SENT) {
            $email->setSentAt(new \DateTimeImmutable());
            $email->setActuallySentFromAddress(new Address($actuallySentFrom));
        }

        return $email;
    }
}
