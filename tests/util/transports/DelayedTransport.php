<?php

declare(strict_types=1);

namespace App\Tests\Util\transports;

use App\Transport\Decorator\AbstractTransportDecorator;
use App\Transport\TransportInterface;
use Symfony\Component\Mailer\Envelope;
use Symfony\Component\Mailer\SentMessage;
use Symfony\Component\Mime\RawMessage;

class DelayedTransport extends AbstractTransportDecorator
{
    /**
     * @param float[] $delaysInSecondsFloat
     */
    public function __construct(TransportInterface $inner, private array $delaysInSecondsFloat)
    {
        parent::__construct($inner);
    }

    public function send(RawMessage $message, Envelope $envelope = null): ?SentMessage
    {
        if (($delay = current($this->delaysInSecondsFloat)) !== false) {
            usleep((int) ($delay * 1_000_000));
            next($this->delaysInSecondsFloat);
        }

        return $this->inner->send($message, $envelope);
    }

    public function __toString(): string
    {
        return "DelayedTransport({$this->inner})";
    }
}
