<?php

declare(strict_types=1);

namespace App\Tests\Util\Factory;

use App\Security\PwUser;
use App\Security\SystemUser;
use App\Security\User;
use App\Security\UserStatus;

class UserFactory
{
    /**
     * @param array<string> $rights
     */
    public static function createPwUser(
        string $serviceId = 'TestServiceId',
        int $userId = 1,
        string $firstname = 'TestVorname',
        string $lastname = 'TestNachName',
        int $agencyId = 2,
        string $agencyName = 'TestAgentur',
        bool $admin = false,
        array $rights = [User::RIGHT_SEND_EMAIL],
        string $status = UserStatus::ACTIVE->value,
    ): PwUser {
        return PwUser::create(
            serviceId: $serviceId,
            payload: [
                'user_id' => $userId,
                'firstname' => $firstname,
                'lastname' => $lastname,
                'agency_id' => $agencyId,
                'agency_name' => $agencyName,
                'admin' => $admin,
                'rights' => $rights,
                'status' => $status,
            ]
        );
    }

    /**
     * @param array<mixed> $payload
     */
    public static function createSystemUser(
        string $serviceId = 'TestService',
        array $payload = ['admin' => false]
    ): SystemUser {
        return SystemUser::create($serviceId, $payload);
    }
}
