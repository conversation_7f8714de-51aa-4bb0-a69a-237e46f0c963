<?php

declare(strict_types=1);

namespace App\Tests\Unit\Email\Provider;

use App\Email\Provider\DomainFactory;
use App\Email\Provider\Ionos;
use App\Email\Provider\OneBlu;
use App\Email\Provider\ProviderConfigInterface;
use App\Email\Provider\Strato;
use Generator;
use PHPUnit\Framework\TestCase;
use Symfony\Component\Mailer\Transport\Dsn;

class ProviderTest extends TestCase
{
    /**
     * @param Dsn[] $supportedDsns
     * @param Dsn[] $unsupportedDsns
     *
     * @test
     * @dataProvider supportedDsnProvider
     */
    public function providerconfigs_support_their_expected_dsns(
        ProviderConfigInterface $providerConfig,
        array $supportedDsns,
        array $unsupportedDsns
    ): void {
        foreach ($supportedDsns as $supportedDsn) {
            self::assertTrue($providerConfig->supportsDsn($supportedDsn));
        }

        foreach ($unsupportedDsns as $unsupportedDsn) {
            self::assertFalse($providerConfig->supportsDsn($unsupportedDsn));
        }
    }

    public static function supportedDsnProvider(): Generator
    {
        yield [new DomainFactory(),
            [
                new Dsn('smtp', 'smtp.df.eu'),
                new Dsn('smtp', 'imap.df.eu'),
                new Dsn('smtp', 'df.eu'),
            ],
            [
                new Dsn('smtp', 'zdf.eu'),
            ],
        ];

        yield [new Strato(),
            [
                new Dsn('smtp', 'smtp.strato.de'),
                new Dsn('smtp', 'imap.strato.de'),
                new Dsn('smtp', 'strato.de'),
            ],
            [
                new Dsn('smtp', 'rato.de'),
            ],
        ];

        yield [new Ionos(),
            [
                new Dsn('smtp', 'smtp.ionos.de'),
                new Dsn('smtp', 'imap.ionos.de'),
                new Dsn('smtp', 'ionos.de'),
            ],
            [
                new Dsn('smtp', 'os.de'),
            ],
        ];

        yield [new OneBlu(),
            [
                new Dsn('smtp', 'smtp.1blu.de'),
                new Dsn('smtp', 'imap.1blu.de'),
                new Dsn('smtp', '1blu.de'),
            ],
            [
                new Dsn('smtp', 'blu.de'),
            ],
        ];
    }
}
